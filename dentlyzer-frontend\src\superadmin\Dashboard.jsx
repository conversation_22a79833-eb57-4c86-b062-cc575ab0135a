import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import axios from 'axios';
import { motion } from 'framer-motion';
import { FaUniversity, FaUserPlus, FaNewspaper, FaUsers } from 'react-icons/fa';
import Navbar from '../student/Navbar';
import Loader from '../components/Loader';
import SuccessModal from '../components/SuccessModal';
import { useAuth } from '../context/AuthContext';
import SuperAdminSidebar from './SuperAdminSidebar';

const SuperAdminDashboard = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showUniversityModal, setShowUniversityModal] = useState(false);
  const [showAccountModal, setShowAccountModal] = useState(false);
  const [showNewsModal, setShowNewsModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [successTitle, setSuccessTitle] = useState('Success');
  const [universities, setUniversities] = useState([]);
  const [analytics, setAnalytics] = useState({ totalUniversities: 0, totalAccounts: 0, recentActivity: [] });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { user, token } = useAuth();
  const location = useLocation();
  const { editUniversity } = location.state || {};

  // University form state
  const [universityForm, setUniversityForm] = useState({
    universityId: '',
    name: { en: '', ar: '' },
    address: { en: '', ar: '' },
    contactInfo: { phone: '', email: '', website: '' },
    description: { en: '', ar: '' },
    establishedYear: '',
    accreditation: { en: '', ar: '' },
    faculties: [],
    programs: [],
    studentCapacity: '',
    tuitionFees: { local: '', international: '' },
    admissionRequirements: { en: '', ar: '' },
    campusSize: '',
    numberOfFaculty: '',
    researchCenters: [],
    partnerships: [],
    rankings: { national: '', international: '' },
    facilities: [],
    scholarships: [],
    languages: [],
    semesterSystem: 'semester',
    academicCalendar: {
      fallStart: '',
      fallEnd: '',
      springStart: '',
      springEnd: '',
      summerStart: '',
      summerEnd: ''
    },
    holidays: ['Friday', 'Sunday']
  });

  // Account form state
  const [accountForm, setAccountForm] = useState({
    email: '',
    password: '',
    name: '',
    role: '',
    studentId: '',
    universityId: ''
  });

  // News form state
  const [newsForm, setNewsForm] = useState({
    title: { en: '', ar: '' },
    content: { en: '', ar: '' },
    category: '',
    priority: 'medium',
    targetAudience: 'all',
    publishDate: new Date().toISOString().split('T')[0],
    expiryDate: '',
    isActive: true
  });

  useEffect(() => {
    const fetchData = async () => {
      if (!user || !token) {
        navigate('/login');
        return;
      }

      try {
        const config = { headers: { Authorization: `Bearer ${token}` } };
        const [universitiesRes, analyticsRes] = await Promise.all([
          axios.get(`${process.env.REACT_APP_API_URL}/api/universities`, config),
          axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/superadmin`, config),
        ]);

        setUniversities(universitiesRes.data || []);
        setAnalytics(analyticsRes.data || { totalUniversities: 0, totalAccounts: 0, recentActivity: [] });
      } catch (err) {
        console.error('Fetch error:', err.response?.data || err.message);
        const errorMessage = err.response?.status === 401
          ? 'Unauthorized. Please log in again.'
          : err.response?.data?.message || 'Failed to load data';
        setError(errorMessage);
        if (err.response?.status === 401) {
          navigate('/login');
        }
      } finally {
        setLoading(false);
      }
    };
    fetchData();

    if (editUniversity) setShowUniversityModal(true);
  }, [user, token, navigate, editUniversity]);

  const handleUniversitySubmit = async (e) => {
    e.preventDefault();
    try {
      if (!universityForm.universityId.trim()) {
        setError('University ID is required');
        return;
      }

      const config = { headers: { Authorization: `Bearer ${token}` } };
      
      if (editUniversity) {
        await axios.put(`${process.env.REACT_APP_API_URL}/api/universities/${universityForm.universityId}`, universityForm, config);
        setUniversities(universities.map(u => u.universityId === universityForm.universityId ? { ...u, ...universityForm } : u));
        setSuccessTitle('University Updated');
        setSuccessMessage('University has been updated successfully!');
      } else {
        const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/universities`, universityForm, config);
        setUniversities([...universities, response.data]);
        setSuccessTitle('University Added');
        setSuccessMessage('University has been added successfully!');
      }
      
      setShowSuccessModal(true);
      setShowUniversityModal(false);
      
      // Reset form
      setUniversityForm({
        universityId: '',
        name: { en: '', ar: '' },
        address: { en: '', ar: '' },
        contactInfo: { phone: '', email: '', website: '' },
        description: { en: '', ar: '' },
        establishedYear: '',
        accreditation: { en: '', ar: '' },
        faculties: [],
        programs: [],
        studentCapacity: '',
        tuitionFees: { local: '', international: '' },
        admissionRequirements: { en: '', ar: '' },
        campusSize: '',
        numberOfFaculty: '',
        researchCenters: [],
        partnerships: [],
        rankings: { national: '', international: '' },
        facilities: [],
        scholarships: [],
        languages: [],
        semesterSystem: 'semester',
        academicCalendar: {
          fallStart: '',
          fallEnd: '',
          springStart: '',
          springEnd: '',
          summerStart: '',
          summerEnd: ''
        },
        holidays: ['Friday', 'Sunday']
      });

      // Refresh analytics
      const analyticsRes = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/superadmin`, config);
      setAnalytics(analyticsRes.data || { totalUniversities: 0, totalAccounts: 0, recentActivity: [] });

      navigate('/superadmin/dashboard', { state: {} });
    } catch (err) {
      console.error('University submission error:', err);
      const errorMessage = err.response?.status === 401
        ? 'Unauthorized. Please log in again.'
        : err.response?.data?.message || 'Failed to save university';
      setError(errorMessage);
    }
  };

  const handleAccountSubmit = async (e) => {
    e.preventDefault();
    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      await axios.post(`${process.env.REACT_APP_API_URL}/api/accounts`, accountForm, config);
      
      setShowAccountModal(false);
      setAccountForm({
        email: '',
        password: '',
        name: '',
        role: '',
        studentId: '',
        universityId: ''
      });
      
      setSuccessTitle('Account Created');
      setSuccessMessage('Account has been created successfully!');
      setShowSuccessModal(true);

      // Refresh analytics
      const analyticsRes = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/superadmin`, config);
      setAnalytics(analyticsRes.data || { totalUniversities: 0, totalAccounts: 0, recentActivity: [] });
    } catch (err) {
      console.error('Account creation error:', err);
      setError(err.response?.data?.message || 'Failed to add account');
    }
  };

  const handleNewsSubmit = async (e) => {
    e.preventDefault();
    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      await axios.post(`${process.env.REACT_APP_API_URL}/api/news`, newsForm, config);
      
      setShowNewsModal(false);
      setNewsForm({
        title: { en: '', ar: '' },
        content: { en: '', ar: '' },
        category: '',
        priority: 'medium',
        targetAudience: 'all',
        publishDate: new Date().toISOString().split('T')[0],
        expiryDate: '',
        isActive: true
      });
      
      setSuccessTitle('News Published');
      setSuccessMessage('News has been published successfully!');
      setShowSuccessModal(true);
    } catch (err) {
      console.error('News creation error:', err);
      setError(err.response?.data?.message || 'Failed to publish news');
    }
  };

  if (loading) return <Loader />;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50">
      <Navbar />
      <SuperAdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      
      <div className="lg:ml-64 pt-16">
        <div className="p-6">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Superadmin Dashboard</h1>
            <p className="text-gray-600">Manage universities, accounts, and system-wide settings</p>
          </div>

          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {error}
            </div>
          )}

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 cursor-pointer"
              onClick={() => setShowUniversityModal(true)}
            >
              <div className="flex items-center">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <FaUniversity className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Add University</h3>
                  <p className="text-gray-600">Create new university</p>
                </div>
              </div>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 cursor-pointer"
              onClick={() => setShowAccountModal(true)}
            >
              <div className="flex items-center">
                <div className="p-3 bg-green-100 rounded-lg">
                  <FaUserPlus className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Add Account</h3>
                  <p className="text-gray-600">Create user account</p>
                </div>
              </div>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 cursor-pointer"
              onClick={() => setShowNewsModal(true)}
            >
              <div className="flex items-center">
                <div className="p-3 bg-purple-100 rounded-lg">
                  <FaNewspaper className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Publish News</h3>
                  <p className="text-gray-600">Create news article</p>
                </div>
              </div>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 cursor-pointer"
              onClick={() => navigate('/superadmin/accounts')}
            >
              <div className="flex items-center">
                <div className="p-3 bg-orange-100 rounded-lg">
                  <FaUsers className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Manage Accounts</h3>
                  <p className="text-gray-600">View all accounts</p>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Analytics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm">Total Universities</p>
                  <p className="text-3xl font-bold text-blue-600">{analytics.totalUniversities || 0}</p>
                </div>
                <FaUniversity className="h-8 w-8 text-blue-600" />
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm">Total Accounts</p>
                  <p className="text-3xl font-bold text-green-600">{analytics.totalAccounts || 0}</p>
                </div>
                <FaUsers className="h-8 w-8 text-green-600" />
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm">Recent Activity</p>
                  <p className="text-3xl font-bold text-purple-600">{analytics.recentActivity?.length || 0}</p>
                </div>
                <FaNewspaper className="h-8 w-8 text-purple-600" />
              </div>
            </div>
          </div>

          {/* Recent Universities */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Universities</h2>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">University ID</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Name</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Contact</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {universities.slice(0, 5).map((university) => (
                    <tr key={university.universityId} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">{university.universityId}</td>
                      <td className="py-3 px-4">{university.name?.en || 'N/A'}</td>
                      <td className="py-3 px-4">{university.contactInfo?.email || 'N/A'}</td>
                      <td className="py-3 px-4">
                        <button
                          onClick={() => navigate('/superadmin/universities')}
                          className="text-blue-600 hover:text-blue-800 font-medium"
                        >
                          View Details
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Success Modal */}
      {showSuccessModal && (
        <SuccessModal
          title={successTitle}
          message={successMessage}
          onClose={() => setShowSuccessModal(false)}
        />
      )}
    </div>
  );
};

export default SuperAdminDashboard;
