{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\superadmin\\\\Dashboard.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport axios from 'axios';\nimport { motion } from 'framer-motion';\nimport { FaUniversity, FaUserPlus, FaNewspaper, FaUsers } from 'react-icons/fa';\nimport Navbar from '../student/Navbar';\nimport Loader from '../components/Loader';\nimport SuccessModal from '../components/SuccessModal';\nimport { useAuth } from '../context/AuthContext';\nimport SuperAdminSidebar from './SuperAdminSidebar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SuperAdminDashboard = () => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [showUniversityModal, setShowUniversityModal] = useState(false);\n  const [showClinicModal, setShowClinicModal] = useState(false);\n  const [showAccountModal, setShowAccountModal] = useState(false);\n  const [showNewsModal, setShowNewsModal] = useState(false);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [successTitle, setSuccessTitle] = useState('Success');\n  const [universities, setUniversities] = useState([]);\n  const [clinics, setClinics] = useState([]);\n  const [analytics, setAnalytics] = useState({\n    totalUniversities: 0,\n    totalClinics: 0,\n    totalAccounts: 0,\n    recentActivity: []\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  const {\n    user,\n    token\n  } = useAuth();\n  const location = useLocation();\n  const {\n    editUniversity,\n    editClinic\n  } = location.state || {};\n  const [universityForm, setUniversityForm] = useState(() => {\n    // If we're editing an existing university\n    if (editUniversity) {\n      // Process the university data for editing\n      const processedUniversity = {\n        ...editUniversity\n      };\n\n      // Format dates properly\n      if (processedUniversity.slotBeginDate) {\n        processedUniversity.slotBeginDate = new Date(processedUniversity.slotBeginDate).toISOString().split('T')[0];\n      }\n      if (processedUniversity.slotEndDate) {\n        processedUniversity.slotEndDate = new Date(processedUniversity.slotEndDate).toISOString().split('T')[0];\n      }\n\n      // Extract available slots from timeSlots if they exist\n      if (Array.isArray(processedUniversity.timeSlots) && processedUniversity.timeSlots.length > 0) {\n        // Get unique time slots\n        processedUniversity.availableSlots = [...new Set(processedUniversity.timeSlots.map(slot => slot.time))];\n      } else {\n        processedUniversity.availableSlots = ['09:00', '11:30', '14:00']; // Default\n      }\n      return processedUniversity;\n    } else {\n      // Default values for a new university\n      return {\n        universityId: '',\n        name: {\n          en: '',\n          ar: ''\n        },\n        description: {\n          en: '',\n          ar: ''\n        },\n        dentistryInfo: {\n          en: '',\n          ar: ''\n        },\n        facilities: {\n          en: '',\n          ar: ''\n        },\n        program: {\n          en: '',\n          ar: ''\n        },\n        dentistryServices: [{\n          en: '',\n          ar: ''\n        }],\n        address: {\n          street: {\n            en: '',\n            ar: ''\n          },\n          city: {\n            en: '',\n            ar: ''\n          },\n          country: {\n            en: '',\n            ar: ''\n          },\n          postalCode: ''\n        },\n        contactInfo: {\n          phone: '',\n          email: '',\n          website: ''\n        },\n        image: '',\n        logo: '',\n        mapUrl: '',\n        slotBeginDate: new Date().toISOString().split('T')[0],\n        slotEndDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],\n        slotDuration: 120,\n        // Default slot duration in minutes\n        availableSlots: ['09:00', '11:30', '14:00'],\n        // Default available time slots\n        holidays: ['Friday', 'Sunday'] // Default holidays\n      };\n    }\n  });\n  const [clinicForm, setClinicForm] = useState(() => {\n    // If we're editing an existing clinic\n    if (editClinic) {\n      // Process the clinic data for editing\n      const processedClinic = {\n        ...editClinic\n      };\n\n      // Format dates properly\n      if (processedClinic.slotBeginDate) {\n        processedClinic.slotBeginDate = new Date(processedClinic.slotBeginDate).toISOString().split('T')[0];\n      }\n      if (processedClinic.slotEndDate) {\n        processedClinic.slotEndDate = new Date(processedClinic.slotEndDate).toISOString().split('T')[0];\n      }\n\n      // Extract available slots from timeSlots if they exist\n      if (Array.isArray(processedClinic.timeSlots) && processedClinic.timeSlots.length > 0) {\n        // Get unique time slots\n        processedClinic.availableSlots = [...new Set(processedClinic.timeSlots.map(slot => slot.time))];\n      } else {\n        processedClinic.availableSlots = ['09:00', '11:00', '13:00', '15:00']; // Default\n      }\n\n      // Ensure services is an array\n      if (!processedClinic.services || !Array.isArray(processedClinic.services)) {\n        processedClinic.services = [];\n      }\n      return processedClinic;\n    } else {\n      // Default values for a new clinic\n      return {\n        dentistId: '',\n        name: {\n          en: '',\n          ar: ''\n        },\n        clinicName: {\n          en: '',\n          ar: ''\n        },\n        about: {\n          en: '',\n          ar: ''\n        },\n        services: [],\n        address: {\n          street: {\n            en: '',\n            ar: ''\n          },\n          city: {\n            en: '',\n            ar: ''\n          },\n          country: {\n            en: '',\n            ar: ''\n          },\n          postalCode: ''\n        },\n        contactInfo: {\n          phone: '',\n          email: '',\n          website: ''\n        },\n        slotBeginDate: new Date().toISOString().split('T')[0],\n        slotEndDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],\n        slotDuration: 60,\n        // Default slot duration in minutes\n        availableSlots: ['09:00', '11:00', '13:00', '15:00'],\n        // Default available time slots\n        workingHours: {\n          monday: '09:00-17:00',\n          tuesday: '09:00-17:00',\n          wednesday: '09:00-17:00',\n          thursday: '09:00-17:00',\n          friday: 'Closed',\n          saturday: '09:00-13:00',\n          sunday: 'Closed'\n        },\n        holidays: ['Friday', 'Sunday'] // Default holidays\n      };\n    }\n  });\n  const [accountForm, setAccountForm] = useState({\n    email: '',\n    password: '',\n    name: '',\n    role: 'student',\n    studentId: '',\n    universityId: '',\n    dentistId: ''\n  });\n  const [newsForm, setNewsForm] = useState({\n    title: '',\n    content: '',\n    isGlobal: true,\n    university: ''\n  });\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!user || !token) {\n        setError('Please log in to view your dashboard.');\n        setLoading(false);\n        return;\n      }\n      try {\n        const config = {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        };\n        // Fetch all data in parallel for better performance\n        const [universitiesRes, clinicsRes, analyticsRes] = await Promise.all([axios.get(`${process.env.REACT_APP_API_URL}/api/universities`, config), axios.get(`${process.env.REACT_APP_API_URL}/api/dentists`, config), axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/superadmin`, config)]);\n        setUniversities(universitiesRes.data || []);\n        setClinics(clinicsRes.data || []);\n        setAnalytics(analyticsRes.data || {\n          totalUniversities: 0,\n          totalClinics: 0,\n          totalAccounts: 0,\n          recentActivity: []\n        });\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response3$data, _err$response4;\n        console.error('Fetch error:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n        const errorMessage = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.status) === 401 ? 'Unauthorized. Please log in again.' : ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || 'Failed to load data';\n        setError(errorMessage);\n        if (((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 401) {\n          navigate('/login');\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n    if (editUniversity) setShowUniversityModal(true);\n    if (editClinic) setShowClinicModal(true);\n  }, [user, token, navigate, editUniversity, editClinic]);\n  const handleUniversitySubmit = async e => {\n    e.preventDefault();\n    try {\n      // Validate required fields on the frontend\n      if (!universityForm.universityId.trim()) {\n        setError('University ID is required');\n        return;\n      }\n      if (!universityForm.name.en.trim() || !universityForm.name.ar.trim()) {\n        setError('University name is required in both English and Arabic');\n        return;\n      }\n      if (!universityForm.contactInfo.phone.trim() || !universityForm.contactInfo.email.trim()) {\n        setError('Contact information (phone and email) is required');\n        return;\n      }\n\n      // Ensure all bilingual fields have at least placeholder values\n      const formToSubmit = {\n        ...universityForm\n      };\n\n      // Set default values for empty bilingual fields\n      ['description', 'dentistryInfo', 'facilities', 'program'].forEach(field => {\n        if (!formToSubmit[field].en.trim() || !formToSubmit[field].ar.trim()) {\n          formToSubmit[field] = {\n            en: formToSubmit[field].en.trim() || 'Not provided',\n            ar: formToSubmit[field].ar.trim() || 'غير متوفر'\n          };\n        }\n      });\n\n      // Ensure dentistry services have values\n      if (formToSubmit.dentistryServices.length === 0) {\n        formToSubmit.dentistryServices = [{\n          en: 'General Dentistry',\n          ar: 'طب الأسنان العام'\n        }];\n      } else {\n        formToSubmit.dentistryServices = formToSubmit.dentistryServices.map(service => ({\n          en: service.en.trim() || 'General Dentistry',\n          ar: service.ar.trim() || 'طب الأسنان العام'\n        }));\n      }\n\n      // Make sure holidays are properly formatted and not empty\n      if (!formToSubmit.holidays || !Array.isArray(formToSubmit.holidays) || formToSubmit.holidays.length === 0) {\n        // If holidays array is empty or not defined, set default values\n        formToSubmit.holidays = [\"Friday\", \"Sunday\"];\n      } else {\n        // Ensure day names are properly capitalized\n        formToSubmit.holidays = formToSubmit.holidays.map(day => {\n          // Capitalize first letter\n          return day.charAt(0).toUpperCase() + day.slice(1).toLowerCase();\n        });\n      }\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      if (editUniversity) {\n        const response = await axios.put(`http://localhost:5000/api/universities/${formToSubmit.universityId}`, formToSubmit, config);\n        setUniversities(universities.map(u => u.universityId === formToSubmit.universityId ? response.data : u));\n        setSuccessTitle('University Updated');\n        setSuccessMessage('University has been updated successfully!');\n        setShowSuccessModal(true);\n      } else {\n        const response = await axios.post('http://localhost:5000/api/universities', formToSubmit, config);\n        setUniversities([...universities, response.data]);\n        setSuccessTitle('University Added');\n        setSuccessMessage('University has been added successfully!');\n        setShowSuccessModal(true);\n      }\n      setShowUniversityModal(false);\n      setUniversityForm({\n        universityId: '',\n        name: {\n          en: '',\n          ar: ''\n        },\n        description: {\n          en: '',\n          ar: ''\n        },\n        dentistryInfo: {\n          en: '',\n          ar: ''\n        },\n        facilities: {\n          en: '',\n          ar: ''\n        },\n        program: {\n          en: '',\n          ar: ''\n        },\n        dentistryServices: [{\n          en: '',\n          ar: ''\n        }],\n        address: {\n          street: {\n            en: '',\n            ar: ''\n          },\n          city: {\n            en: '',\n            ar: ''\n          },\n          country: {\n            en: '',\n            ar: ''\n          },\n          postalCode: ''\n        },\n        contactInfo: {\n          phone: '',\n          email: '',\n          website: ''\n        },\n        image: '',\n        logo: '',\n        mapUrl: '',\n        slotBeginDate: new Date().toISOString().split('T')[0],\n        slotEndDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],\n        slotDuration: 120,\n        // Default slot duration in minutes\n        availableSlots: ['09:00', '11:30', '14:00'],\n        // Default available time slots\n        holidays: ['Friday', 'Sunday'] // Default holidays\n      });\n\n      // Refresh analytics to update university count\n      const analyticsRes = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/superadmin`, config);\n      setAnalytics(analyticsRes.data || {\n        totalUniversities: 0,\n        totalClinics: 0,\n        totalAccounts: 0,\n        recentActivity: []\n      });\n      navigate('/superadmin/dashboard', {\n        state: {}\n      });\n    } catch (err) {\n      var _err$response5, _err$response5$data, _err$response6, _err$response6$data;\n      console.error('University submission error:', err);\n\n      // Create a more detailed error message\n      let errorMessage = 'Failed to save university. ';\n      if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.error) {\n        // If the backend sends a detailed error message\n        errorMessage += err.response.data.error;\n      } else if ((_err$response6 = err.response) !== null && _err$response6 !== void 0 && (_err$response6$data = _err$response6.data) !== null && _err$response6$data !== void 0 && _err$response6$data.message) {\n        // If the backend sends a simple message\n        errorMessage += err.response.data.message;\n      } else if (err.message) {\n        // If there's a general error message\n        errorMessage += err.message;\n      }\n      setError(errorMessage);\n    }\n  };\n  const handleClinicSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    try {\n      // Auto-generate dentistId if not provided and not editing\n      if (!editClinic && !clinicForm.dentistId.trim()) {\n        // Generate a unique ID with 'D' prefix and a random number\n        const randomId = 'D' + Math.floor(100000 + Math.random() * 900000);\n        clinicForm.dentistId = randomId;\n        console.log('Auto-generated dentistId:', randomId);\n      } else if (editClinic && !clinicForm.dentistId.trim()) {\n        setError('Dentist ID is required');\n        return;\n      }\n      if (!clinicForm.name.en.trim() || !clinicForm.name.ar.trim()) {\n        setError('Dentist name is required in both English and Arabic');\n        return;\n      }\n      if (!clinicForm.clinicName.en.trim() || !clinicForm.clinicName.ar.trim()) {\n        setError('Clinic name is required in both English and Arabic');\n        return;\n      }\n      if (!clinicForm.contactInfo.phone.trim()) {\n        setError('Phone number is required');\n        return;\n      }\n      if (!clinicForm.contactInfo.email || !clinicForm.contactInfo.email.trim()) {\n        setError('Email is required');\n        return;\n      }\n\n      // Validate email format\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!emailRegex.test(clinicForm.contactInfo.email.trim())) {\n        setError('Please enter a valid email address');\n        return;\n      }\n\n      // Only validate city as required in address fields\n      if (!clinicForm.address.city.en.trim() || !clinicForm.address.city.ar.trim()) {\n        setError('City is required in both English and Arabic');\n        return;\n      }\n      if (!clinicForm.slotBeginDate || !clinicForm.slotEndDate) {\n        setError('Slot begin and end dates are required');\n        return;\n      }\n      if (!clinicForm.slotDuration || clinicForm.slotDuration < 30) {\n        setError('Valid slot duration is required (minimum 30 minutes)');\n        return;\n      }\n      if (!clinicForm.availableSlots || clinicForm.availableSlots.length === 0) {\n        setError('At least one available time slot is required');\n        return;\n      }\n\n      // Create a copy of the form data to modify\n      const formToSubmit = {\n        ...clinicForm\n      };\n\n      // Add email at the root level to match the schema\n      formToSubmit.email = formToSubmit.contactInfo.email.trim();\n\n      // Ensure all required bilingual fields have values\n      ['about'].forEach(field => {\n        if (!formToSubmit[field].en.trim() || !formToSubmit[field].ar.trim()) {\n          formToSubmit[field] = {\n            en: formToSubmit[field].en.trim() || 'Not provided',\n            ar: formToSubmit[field].ar.trim() || 'غير متوفر'\n          };\n        }\n      });\n\n      // Ensure services have values\n      if (!formToSubmit.services || formToSubmit.services.length === 0) {\n        formToSubmit.services = [{\n          en: 'General Dentistry',\n          ar: 'طب الأسنان العام'\n        }];\n      } else {\n        formToSubmit.services = formToSubmit.services.map(service => ({\n          en: service.en.trim() || 'General Dentistry',\n          ar: service.ar.trim() || 'طب الأسنان العام'\n        }));\n      }\n\n      // Make sure holidays are properly formatted and not empty\n      if (!formToSubmit.holidays || !Array.isArray(formToSubmit.holidays) || formToSubmit.holidays.length === 0) {\n        // If holidays array is empty or not defined, set default values\n        formToSubmit.holidays = [\"Friday\", \"Sunday\"];\n      } else {\n        // Ensure day names are properly capitalized\n        formToSubmit.holidays = formToSubmit.holidays.map(day => {\n          // Capitalize first letter\n          return day.charAt(0).toUpperCase() + day.slice(1).toLowerCase();\n        });\n      }\n\n      // Ensure working hours are properly formatted\n      const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];\n      days.forEach(day => {\n        if (!formToSubmit.workingHours[day]) {\n          formToSubmit.workingHours[day] = 'Closed';\n        }\n      });\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      console.log('Submitting clinic data:', formToSubmit);\n      if (editClinic) {\n        const response = await axios.put(`${process.env.REACT_APP_API_URL}/api/dentists/${formToSubmit.dentistId}`, formToSubmit, config);\n        setClinics(clinics.map(c => c.dentistId === formToSubmit.dentistId ? response.data : c));\n        setSuccessTitle('Clinic Updated');\n        setSuccessMessage('Clinic has been updated successfully!');\n        setShowSuccessModal(true);\n      } else {\n        const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/dentists`, formToSubmit, config);\n        setClinics([...clinics, response.data]);\n        setSuccessTitle('Clinic Added');\n        setSuccessMessage('Clinic has been added successfully!');\n        setShowSuccessModal(true);\n      }\n      setShowClinicModal(false);\n      setClinicForm({\n        dentistId: '',\n        name: {\n          en: '',\n          ar: ''\n        },\n        clinicName: {\n          en: '',\n          ar: ''\n        },\n        about: {\n          en: '',\n          ar: ''\n        },\n        services: [],\n        address: {\n          street: {\n            en: '',\n            ar: ''\n          },\n          city: {\n            en: '',\n            ar: ''\n          },\n          country: {\n            en: '',\n            ar: ''\n          },\n          postalCode: ''\n        },\n        contactInfo: {\n          phone: '',\n          email: '',\n          website: ''\n        },\n        slotBeginDate: new Date().toISOString().split('T')[0],\n        slotEndDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],\n        slotDuration: 60,\n        // Default slot duration in minutes\n        availableSlots: ['09:00', '11:00', '13:00', '15:00'],\n        // Default available time slots\n        workingHours: {\n          monday: '09:00-17:00',\n          tuesday: '09:00-17:00',\n          wednesday: '09:00-17:00',\n          thursday: '09:00-17:00',\n          friday: 'Closed',\n          saturday: '09:00-13:00',\n          sunday: 'Closed'\n        },\n        holidays: ['Friday', 'Sunday'] // Default holidays\n      });\n\n      // Refresh analytics to update clinic count\n      const analyticsRes = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/superadmin`, config);\n      setAnalytics(analyticsRes.data || {\n        totalUniversities: 0,\n        totalClinics: 0,\n        totalAccounts: 0,\n        recentActivity: []\n      });\n      navigate('/superadmin/dashboard', {\n        state: {}\n      });\n    } catch (err) {\n      var _err$response7, _err$response8, _err$response9, _err$response10, _err$response10$data, _err$response11, _err$response11$data;\n      console.error('Clinic submission error:', err);\n\n      // Log detailed error information for debugging\n      if ((_err$response7 = err.response) !== null && _err$response7 !== void 0 && _err$response7.data) {\n        console.error('Server response data:', err.response.data);\n      }\n      if ((_err$response8 = err.response) !== null && _err$response8 !== void 0 && _err$response8.status) {\n        console.error('Server response status:', err.response.status);\n      }\n      if ((_err$response9 = err.response) !== null && _err$response9 !== void 0 && _err$response9.headers) {\n        console.error('Server response headers:', err.response.headers);\n      }\n\n      // Create a detailed error message\n      let errorMessage = 'Failed to save clinic. ';\n      if ((_err$response10 = err.response) !== null && _err$response10 !== void 0 && (_err$response10$data = _err$response10.data) !== null && _err$response10$data !== void 0 && _err$response10$data.error) {\n        // If the backend sends a detailed error message\n        errorMessage += err.response.data.error;\n      } else if ((_err$response11 = err.response) !== null && _err$response11 !== void 0 && (_err$response11$data = _err$response11.data) !== null && _err$response11$data !== void 0 && _err$response11$data.message) {\n        // If the backend sends a simple message\n        errorMessage += err.response.data.message;\n      } else if (err.message) {\n        // If there's a general error message\n        errorMessage += err.message;\n      }\n      setError(errorMessage);\n    }\n  };\n  const handleAccountSubmit = async e => {\n    e.preventDefault();\n    try {\n      // Validate form based on role\n      if (accountForm.role === 'student' && !accountForm.studentId) {\n        setError('Student ID is required for student accounts');\n        return;\n      }\n      if (['student', 'supervisor', 'admin', 'assistant'].includes(accountForm.role) && !accountForm.universityId) {\n        setError('University ID is required for this role');\n        return;\n      }\n      if (accountForm.role === 'assistant' && !accountForm.dentistId) {\n        setError('Dentist ID is required for assistant accounts');\n        return;\n      }\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      await axios.post('http://localhost:5000/api/accounts', accountForm, config);\n      setShowAccountModal(false);\n      setAccountForm({\n        email: '',\n        password: '',\n        name: '',\n        role: 'student',\n        studentId: '',\n        universityId: '',\n        dentistId: ''\n      });\n\n      // Show success message\n      setSuccessTitle('Account Created');\n      setSuccessMessage('Account has been created successfully!');\n      setShowSuccessModal(true);\n\n      // Refresh analytics to update account count\n      const analyticsRes = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/superadmin`, config);\n      setAnalytics(analyticsRes.data || {\n        totalUniversities: 0,\n        totalClinics: 0,\n        totalAccounts: 0,\n        recentActivity: []\n      });\n    } catch (err) {\n      var _err$response12, _err$response12$data;\n      console.error('Account creation error:', err);\n      setError(((_err$response12 = err.response) === null || _err$response12 === void 0 ? void 0 : (_err$response12$data = _err$response12.data) === null || _err$response12$data === void 0 ? void 0 : _err$response12$data.message) || 'Failed to add account');\n    }\n  };\n  const handleNewsSubmit = async e => {\n    e.preventDefault();\n    try {\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      await axios.post('http://localhost:5000/api/news', newsForm, config);\n      setShowNewsModal(false);\n      setNewsForm({\n        title: '',\n        content: '',\n        isGlobal: true,\n        university: ''\n      });\n      // Show success message\n      setSuccessTitle('News Sent');\n      setSuccessMessage('News has been sent successfully!');\n      setShowSuccessModal(true);\n    } catch (err) {\n      var _err$response13, _err$response13$data;\n      console.error('News submission error:', err);\n      setError(((_err$response13 = err.response) === null || _err$response13 === void 0 ? void 0 : (_err$response13$data = _err$response13.data) === null || _err$response13$data === void 0 ? void 0 : _err$response13$data.message) || 'Failed to send news');\n    }\n  };\n  const container = {\n    hidden: {\n      opacity: 0\n    },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 598,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(SuperAdminSidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 603,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 606,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [error && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-5 w-5 text-red-500 mr-3\",\n                viewBox: \"0 0 20 20\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-700 font-medium\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\",\n                  children: \"Super Admin Dashboard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#333333]\",\n                  children: [\"Welcome back, \", (user === null || user === void 0 ? void 0 : user.name) || 'Super Admin']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-3 w-full sm:w-auto\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => setShowUniversityModal(true),\n                  className: \"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FaUniversity, {\n                    className: \"h-5 w-5 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 644,\n                    columnNumber: 21\n                  }, this), \"Add University\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => setShowAccountModal(true),\n                  className: \"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FaUserPlus, {\n                    className: \"h-5 w-5 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 653,\n                    columnNumber: 21\n                  }, this), \"Add Account\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              whileInView: \"show\",\n              viewport: {\n                once: true\n              },\n              className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                variants: item,\n                className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#0077B6] group cursor-pointer\",\n                onClick: () => navigate('/superadmin/universities'),\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[rgba(0,119,182,0.1)] w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-[rgba(0,119,182,0.2)] transition-colors duration-300\",\n                    children: /*#__PURE__*/_jsxDEV(FaUniversity, {\n                      className: \"h-6 w-6 text-[#0077B6]\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 673,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 672,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Total Universities\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 676,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: analytics.totalUniversities\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 677,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 675,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 671,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                variants: item,\n                className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#0077B6] group cursor-pointer\",\n                onClick: () => navigate('/superadmin/accounts'),\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[rgba(0,119,182,0.1)] w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-[rgba(0,119,182,0.2)] transition-colors duration-300\",\n                    children: /*#__PURE__*/_jsxDEV(FaUsers, {\n                      className: \"h-6 w-6 text-[#0077B6]\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 689,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 688,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Total Accounts\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 692,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: analytics.totalAccounts\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 693,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 691,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                variants: item,\n                className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#0077B6] group cursor-pointer\",\n                onClick: () => navigate('/superadmin/news'),\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[rgba(0,119,182,0.1)] w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-[rgba(0,119,182,0.2)] transition-colors duration-300\",\n                    children: /*#__PURE__*/_jsxDEV(FaNewspaper, {\n                      className: \"h-6 w-6 text-[#0077B6]\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 705,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 704,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"News\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 708,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: e => {\n                        e.stopPropagation(); // Prevent navigation when clicking the button\n                        setShowNewsModal(true);\n                      },\n                      className: \"text-[#0077B6] hover:underline text-sm font-medium\",\n                      children: \"Create Announcement\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 709,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 707,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.2\n              },\n              className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6] flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: \"h-5 w-5 mr-2 text-[#0077B6]\",\n                      viewBox: \"0 0 20 20\",\n                      fill: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 733,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 732,\n                      columnNumber: 23\n                    }, this), \"Recent Activity\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 731,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => navigate('/superadmin/activity'),\n                    className: \"text-[#0077B6] hover:underline font-medium\",\n                    children: \"View All\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 737,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 730,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"overflow-x-auto\",\n                  children: /*#__PURE__*/_jsxDEV(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                      className: \"bg-gray-50\",\n                      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Action\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 749,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"User\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 750,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Date\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 751,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 748,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 747,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                      className: \"bg-white divide-y divide-gray-200\",\n                      children: analytics.recentActivity.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: /*#__PURE__*/_jsxDEV(\"td\", {\n                          colSpan: \"3\",\n                          className: \"px-6 py-8 text-center\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col items-center justify-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                              xmlns: \"http://www.w3.org/2000/svg\",\n                              className: \"h-12 w-12 text-gray-400 mb-4\",\n                              fill: \"none\",\n                              viewBox: \"0 0 24 24\",\n                              stroke: \"currentColor\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 760,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 759,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                              className: \"text-lg font-medium text-gray-900\",\n                              children: \"No recent activity\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 762,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"mt-1 text-gray-500\",\n                              children: \"No actions have been logged recently.\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 763,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 758,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 757,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 756,\n                        columnNumber: 27\n                      }, this) : analytics.recentActivity.map((activity, index) => /*#__PURE__*/_jsxDEV(motion.tr, {\n                        initial: {\n                          opacity: 0\n                        },\n                        animate: {\n                          opacity: 1\n                        },\n                        className: \"hover:bg-gray-50\",\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                          children: activity.action\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 775,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                          children: activity.user\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 776,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                          children: new Date(activity.date).toLocaleDateString('en-US', {\n                            weekday: 'short',\n                            month: 'short',\n                            day: 'numeric'\n                          })\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 777,\n                          columnNumber: 31\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 769,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 754,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 746,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 745,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 608,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 605,\n      columnNumber: 7\n    }, this), showUniversityModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-[#0077B6]\",\n              children: editUniversity ? 'Edit University' : 'Add University'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setShowUniversityModal(false);\n                navigate('/superadmin/dashboard', {\n                  state: {}\n                });\n              },\n              className: \"text-gray-400 hover:text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 807,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 806,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 805,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 803,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleUniversitySubmit,\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"University ID*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 814,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: universityForm.universityId,\n                  onChange: e => setUniversityForm({\n                    ...universityForm,\n                    universityId: e.target.value\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                  required: true,\n                  disabled: editUniversity\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 815,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Name (English)*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 825,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: universityForm.name.en,\n                  onChange: e => setUniversityForm({\n                    ...universityForm,\n                    name: {\n                      ...universityForm.name,\n                      en: e.target.value\n                    }\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 824,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Name (Arabic)*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 835,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: universityForm.name.ar,\n                  onChange: e => setUniversityForm({\n                    ...universityForm,\n                    name: {\n                      ...universityForm.name,\n                      ar: e.target.value\n                    }\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 836,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 834,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Phone*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 845,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  value: universityForm.contactInfo.phone,\n                  onChange: e => setUniversityForm({\n                    ...universityForm,\n                    contactInfo: {\n                      ...universityForm.contactInfo,\n                      phone: e.target.value\n                    }\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 846,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Email*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 855,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  value: universityForm.contactInfo.email,\n                  onChange: e => setUniversityForm({\n                    ...universityForm,\n                    contactInfo: {\n                      ...universityForm.contactInfo,\n                      email: e.target.value\n                    }\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 856,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 854,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Website\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 865,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"url\",\n                  value: universityForm.contactInfo.website,\n                  onChange: e => setUniversityForm({\n                    ...universityForm,\n                    contactInfo: {\n                      ...universityForm.contactInfo,\n                      website: e.target.value\n                    }\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 866,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 864,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Logo URL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 874,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"url\",\n                  value: universityForm.logo,\n                  onChange: e => setUniversityForm({\n                    ...universityForm,\n                    logo: e.target.value\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 875,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Image URL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 883,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"url\",\n                  value: universityForm.image,\n                  onChange: e => setUniversityForm({\n                    ...universityForm,\n                    image: e.target.value\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 884,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 882,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Map URL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 892,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"url\",\n                  value: universityForm.mapUrl,\n                  onChange: e => setUniversityForm({\n                    ...universityForm,\n                    mapUrl: e.target.value\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 893,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 891,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Slot Begin Date*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 901,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: universityForm.slotBeginDate,\n                  onChange: e => setUniversityForm({\n                    ...universityForm,\n                    slotBeginDate: e.target.value\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 902,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 900,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Slot End Date*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 911,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: universityForm.slotEndDate,\n                  onChange: e => setUniversityForm({\n                    ...universityForm,\n                    slotEndDate: e.target.value\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 912,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 910,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 812,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Description (English)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 922,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: universityForm.description.en,\n                onChange: e => setUniversityForm({\n                  ...universityForm,\n                  description: {\n                    ...universityForm.description,\n                    en: e.target.value\n                  }\n                }),\n                rows: \"3\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 923,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 921,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Description (Arabic)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 931,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: universityForm.description.ar,\n                onChange: e => setUniversityForm({\n                  ...universityForm,\n                  description: {\n                    ...universityForm.description,\n                    ar: e.target.value\n                  }\n                }),\n                rows: \"3\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 932,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 930,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Dentistry Info (English)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 940,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: universityForm.dentistryInfo.en,\n                onChange: e => setUniversityForm({\n                  ...universityForm,\n                  dentistryInfo: {\n                    ...universityForm.dentistryInfo,\n                    en: e.target.value\n                  }\n                }),\n                rows: \"3\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 941,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 939,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Dentistry Info (Arabic)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 949,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: universityForm.dentistryInfo.ar,\n                onChange: e => setUniversityForm({\n                  ...universityForm,\n                  dentistryInfo: {\n                    ...universityForm.dentistryInfo,\n                    ar: e.target.value\n                  }\n                }),\n                rows: \"3\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 950,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 948,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Facilities (English)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: universityForm.facilities.en,\n                onChange: e => setUniversityForm({\n                  ...universityForm,\n                  facilities: {\n                    ...universityForm.facilities,\n                    en: e.target.value\n                  }\n                }),\n                rows: \"3\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 959,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 957,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Facilities (Arabic)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 967,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: universityForm.facilities.ar,\n                onChange: e => setUniversityForm({\n                  ...universityForm,\n                  facilities: {\n                    ...universityForm.facilities,\n                    ar: e.target.value\n                  }\n                }),\n                rows: \"3\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 968,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 966,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Program (English)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 976,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: universityForm.program.en,\n                onChange: e => setUniversityForm({\n                  ...universityForm,\n                  program: {\n                    ...universityForm.program,\n                    en: e.target.value\n                  }\n                }),\n                rows: \"3\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 975,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Program (Arabic)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 985,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: universityForm.program.ar,\n                onChange: e => setUniversityForm({\n                  ...universityForm,\n                  program: {\n                    ...universityForm.program,\n                    ar: e.target.value\n                  }\n                }),\n                rows: \"3\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 986,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 984,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Dentistry Services\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 995,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [universityForm.dentistryServices.map((service, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 border border-gray-200 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-xs font-medium text-gray-700 mb-1\",\n                      children: \"Service (English)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1000,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: service.en,\n                      onChange: e => {\n                        const newServices = [...universityForm.dentistryServices];\n                        newServices[index] = {\n                          ...newServices[index],\n                          en: e.target.value\n                        };\n                        setUniversityForm({\n                          ...universityForm,\n                          dentistryServices: newServices\n                        });\n                      },\n                      className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1001,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 999,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-xs font-medium text-gray-700 mb-1\",\n                      children: \"Service (Arabic)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1013,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: service.ar,\n                      onChange: e => {\n                        const newServices = [...universityForm.dentistryServices];\n                        newServices[index] = {\n                          ...newServices[index],\n                          ar: e.target.value\n                        };\n                        setUniversityForm({\n                          ...universityForm,\n                          dentistryServices: newServices\n                        });\n                      },\n                      className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1014,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1012,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"sm:col-span-2 flex justify-end\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => {\n                        const newServices = [...universityForm.dentistryServices];\n                        newServices.splice(index, 1);\n                        setUniversityForm({\n                          ...universityForm,\n                          dentistryServices: newServices\n                        });\n                      },\n                      className: \"text-red-500 hover:text-red-700\",\n                      children: \"Remove\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1026,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1025,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 998,\n                  columnNumber: 23\n                }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => {\n                    setUniversityForm({\n                      ...universityForm,\n                      dentistryServices: [...universityForm.dentistryServices, {\n                        en: '',\n                        ar: ''\n                      }]\n                    });\n                  },\n                  className: \"px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors\",\n                  children: \"+ Add Service\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1040,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 996,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 994,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Slot Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1055,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2 p-4 border border-gray-200 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-xs font-medium text-gray-700 mb-1\",\n                    children: \"Slot Duration (minutes)*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1058,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    value: universityForm.slotDuration,\n                    onChange: e => setUniversityForm({\n                      ...universityForm,\n                      slotDuration: parseInt(e.target.value)\n                    }),\n                    min: \"30\",\n                    step: \"30\",\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1059,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1057,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-xs font-medium text-gray-700 mb-1\",\n                    children: \"Available Time Slots*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1073,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-2\",\n                    children: [universityForm.availableSlots.map((slot, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"time\",\n                        value: slot,\n                        onChange: e => {\n                          const newSlots = [...universityForm.availableSlots];\n                          newSlots[index] = e.target.value;\n                          setUniversityForm({\n                            ...universityForm,\n                            availableSlots: newSlots\n                          });\n                        },\n                        className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                        required: true\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1077,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: () => {\n                          const newSlots = [...universityForm.availableSlots];\n                          newSlots.splice(index, 1);\n                          setUniversityForm({\n                            ...universityForm,\n                            availableSlots: newSlots\n                          });\n                        },\n                        className: \"text-red-500 hover:text-red-700\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          className: \"h-5 w-5\",\n                          viewBox: \"0 0 20 20\",\n                          fill: \"currentColor\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z\",\n                            clipRule: \"evenodd\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1098,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1097,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1088,\n                        columnNumber: 29\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1076,\n                      columnNumber: 27\n                    }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => {\n                        setUniversityForm({\n                          ...universityForm,\n                          availableSlots: [...universityForm.availableSlots, '09:00']\n                        });\n                      },\n                      className: \"px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors w-full\",\n                      children: \"+ Add Time Slot\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1103,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1074,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1072,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1056,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1054,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Holidays\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1121,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 mt-2 p-4 border border-gray-200 rounded-lg\",\n                children: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map(day => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    id: `holiday-${day}`,\n                    checked: universityForm.holidays.includes(day),\n                    onChange: () => {\n                      const currentHolidays = [...universityForm.holidays];\n                      if (currentHolidays.includes(day)) {\n                        // Remove the day if it's already in the holidays array\n                        setUniversityForm({\n                          ...universityForm,\n                          holidays: currentHolidays.filter(holiday => holiday !== day)\n                        });\n                      } else {\n                        // Add the day if it's not in the holidays array\n                        setUniversityForm({\n                          ...universityForm,\n                          holidays: [...currentHolidays, day]\n                        });\n                      }\n                    },\n                    className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1125,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: `holiday-${day}`,\n                    className: \"ml-2 block text-sm text-gray-700\",\n                    children: day\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1147,\n                    columnNumber: 25\n                  }, this)]\n                }, day, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1124,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1122,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-gray-500\",\n                children: \"Select days when the university is closed.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1153,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1120,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Address\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1157,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-xs font-medium text-gray-700 mb-1\",\n                    children: \"Street (English)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1160,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: universityForm.address.street.en,\n                    onChange: e => setUniversityForm({\n                      ...universityForm,\n                      address: {\n                        ...universityForm.address,\n                        street: {\n                          ...universityForm.address.street,\n                          en: e.target.value\n                        }\n                      }\n                    }),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1161,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1159,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-xs font-medium text-gray-700 mb-1\",\n                    children: \"Street (Arabic)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1178,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: universityForm.address.street.ar,\n                    onChange: e => setUniversityForm({\n                      ...universityForm,\n                      address: {\n                        ...universityForm.address,\n                        street: {\n                          ...universityForm.address.street,\n                          ar: e.target.value\n                        }\n                      }\n                    }),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1179,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1177,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-xs font-medium text-gray-700 mb-1\",\n                    children: \"City (English)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1196,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: universityForm.address.city.en,\n                    onChange: e => setUniversityForm({\n                      ...universityForm,\n                      address: {\n                        ...universityForm.address,\n                        city: {\n                          ...universityForm.address.city,\n                          en: e.target.value\n                        }\n                      }\n                    }),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1197,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1195,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-xs font-medium text-gray-700 mb-1\",\n                    children: \"City (Arabic)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1214,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: universityForm.address.city.ar,\n                    onChange: e => setUniversityForm({\n                      ...universityForm,\n                      address: {\n                        ...universityForm.address,\n                        city: {\n                          ...universityForm.address.city,\n                          ar: e.target.value\n                        }\n                      }\n                    }),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1215,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1213,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-xs font-medium text-gray-700 mb-1\",\n                    children: \"Country (English)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1232,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: universityForm.address.country.en,\n                    onChange: e => setUniversityForm({\n                      ...universityForm,\n                      address: {\n                        ...universityForm.address,\n                        country: {\n                          ...universityForm.address.country,\n                          en: e.target.value\n                        }\n                      }\n                    }),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1233,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1231,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-xs font-medium text-gray-700 mb-1\",\n                    children: \"Country (Arabic)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1250,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: universityForm.address.country.ar,\n                    onChange: e => setUniversityForm({\n                      ...universityForm,\n                      address: {\n                        ...universityForm.address,\n                        country: {\n                          ...universityForm.address.country,\n                          ar: e.target.value\n                        }\n                      }\n                    }),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1251,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1249,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-xs font-medium text-gray-700 mb-1\",\n                    children: \"Postal Code\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1268,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: universityForm.address.postalCode,\n                    onChange: e => setUniversityForm({\n                      ...universityForm,\n                      address: {\n                        ...universityForm.address,\n                        postalCode: e.target.value\n                      }\n                    }),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1269,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1267,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1158,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1156,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-4 pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                type: \"button\",\n                onClick: () => {\n                  setShowUniversityModal(false);\n                  navigate('/superadmin/dashboard', {\n                    state: {}\n                  });\n                },\n                className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\",\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1285,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                type: \"submit\",\n                className: \"px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 font-medium transition-colors shadow-md hover:shadow-lg\",\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: editUniversity ? 'Update University' : 'Add University'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1294,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1284,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 811,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 802,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 797,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 796,\n      columnNumber: 9\n    }, this), showClinicModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-blue-900\",\n              children: editClinic ? 'Edit Clinic' : 'Add Clinic'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1318,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setShowClinicModal(false);\n                navigate('/superadmin/dashboard', {\n                  state: {}\n                });\n              },\n              className: \"text-gray-400 hover:text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1321,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1320,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1319,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1317,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleClinicSubmit,\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Dentist ID*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1328,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: clinicForm.dentistId,\n                  onChange: e => setClinicForm({\n                    ...clinicForm,\n                    dentistId: e.target.value\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true,\n                  disabled: editClinic\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1329,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1327,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Name (English)*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1339,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: clinicForm.name.en,\n                  onChange: e => setClinicForm({\n                    ...clinicForm,\n                    name: {\n                      ...clinicForm.name,\n                      en: e.target.value\n                    }\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1340,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1338,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Name (Arabic)*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1349,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: clinicForm.name.ar,\n                  onChange: e => setClinicForm({\n                    ...clinicForm,\n                    name: {\n                      ...clinicForm.name,\n                      ar: e.target.value\n                    }\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue- atre-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1350,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1348,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Clinic Name (English)*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1359,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: clinicForm.clinicName.en,\n                  onChange: e => setClinicForm({\n                    ...clinicForm,\n                    clinicName: {\n                      ...clinicForm.clinicName,\n                      en: e.target.value\n                    }\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1360,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1358,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Clinic Name (Arabic)*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1369,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: clinicForm.clinicName.ar,\n                  onChange: e => setClinicForm({\n                    ...clinicForm,\n                    clinicName: {\n                      ...clinicForm.clinicName,\n                      ar: e.target.value\n                    }\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1370,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1368,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"City (English)*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1379,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: clinicForm.address.city.en,\n                  onChange: e => setClinicForm({\n                    ...clinicForm,\n                    address: {\n                      ...clinicForm.address,\n                      city: {\n                        ...clinicForm.address.city,\n                        en: e.target.value\n                      }\n                    }\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1380,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1378,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"City (Arabic)*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1389,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: clinicForm.address.city.ar,\n                  onChange: e => setClinicForm({\n                    ...clinicForm,\n                    address: {\n                      ...clinicForm.address,\n                      city: {\n                        ...clinicForm.address.city,\n                        ar: e.target.value\n                      }\n                    }\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1390,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1388,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Phone*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1399,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  value: clinicForm.contactInfo.phone,\n                  onChange: e => setClinicForm({\n                    ...clinicForm,\n                    contactInfo: {\n                      ...clinicForm.contactInfo,\n                      phone: e.target.value\n                    }\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1400,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1398,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Email*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1409,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  value: clinicForm.contactInfo.email,\n                  onChange: e => setClinicForm({\n                    ...clinicForm,\n                    contactInfo: {\n                      ...clinicForm.contactInfo,\n                      email: e.target.value\n                    }\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1410,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1408,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Slot Begin Date*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1419,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: clinicForm.slotBeginDate,\n                  onChange: e => setClinicForm({\n                    ...clinicForm,\n                    slotBeginDate: e.target.value\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1420,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1418,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Slot End Date*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1429,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: clinicForm.slotEndDate,\n                  onChange: e => setClinicForm({\n                    ...clinicForm,\n                    slotEndDate: e.target.value\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1430,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1428,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1326,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: [\"About (English) \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-400 text-xs\",\n                  children: \"(Optional)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1440,\n                  columnNumber: 99\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1440,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: clinicForm.about.en,\n                onChange: e => setClinicForm({\n                  ...clinicForm,\n                  about: {\n                    ...clinicForm.about,\n                    en: e.target.value\n                  }\n                }),\n                rows: \"3\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1441,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1439,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: [\"About (Arabic) \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-400 text-xs\",\n                  children: \"(Optional)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1449,\n                  columnNumber: 98\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1449,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: clinicForm.about.ar,\n                onChange: e => setClinicForm({\n                  ...clinicForm,\n                  about: {\n                    ...clinicForm.about,\n                    ar: e.target.value\n                  }\n                }),\n                rows: \"3\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1450,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1448,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: [\"Services \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-400 text-xs\",\n                  children: \"(Optional)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1459,\n                  columnNumber: 92\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1459,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [clinicForm.services.map((service, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 border border-gray-200 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-xs font-medium text-gray-700 mb-1\",\n                      children: \"Service (English)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1464,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: service.en,\n                      onChange: e => {\n                        const newServices = [...clinicForm.services];\n                        newServices[index] = {\n                          ...newServices[index],\n                          en: e.target.value\n                        };\n                        setClinicForm({\n                          ...clinicForm,\n                          services: newServices\n                        });\n                      },\n                      className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1465,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1463,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-xs font-medium text-gray-700 mb-1\",\n                      children: \"Service (Arabic)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1477,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: service.ar,\n                      onChange: e => {\n                        const newServices = [...clinicForm.services];\n                        newServices[index] = {\n                          ...newServices[index],\n                          ar: e.target.value\n                        };\n                        setClinicForm({\n                          ...clinicForm,\n                          services: newServices\n                        });\n                      },\n                      className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1478,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1476,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"sm:col-span-2 flex justify-end\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => {\n                        const newServices = [...clinicForm.services];\n                        newServices.splice(index, 1);\n                        setClinicForm({\n                          ...clinicForm,\n                          services: newServices\n                        });\n                      },\n                      className: \"text-red-500 hover:text-red-700\",\n                      children: \"Remove\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1490,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1489,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1462,\n                  columnNumber: 23\n                }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => {\n                    setClinicForm({\n                      ...clinicForm,\n                      services: [...clinicForm.services, {\n                        en: '',\n                        ar: ''\n                      }]\n                    });\n                  },\n                  className: \"px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors\",\n                  children: \"+ Add Service\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1504,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1460,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1458,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Slot Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1520,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2 p-4 border border-gray-200 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-xs font-medium text-gray-700 mb-1\",\n                    children: \"Slot Duration (minutes)*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1523,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    value: clinicForm.slotDuration,\n                    onChange: e => setClinicForm({\n                      ...clinicForm,\n                      slotDuration: parseInt(e.target.value)\n                    }),\n                    min: \"30\",\n                    step: \"30\",\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1524,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1522,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-xs font-medium text-gray-700 mb-1\",\n                    children: \"Available Time Slots*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1538,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-2\",\n                    children: [clinicForm.availableSlots.map((slot, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"time\",\n                        value: slot,\n                        onChange: e => {\n                          const newSlots = [...clinicForm.availableSlots];\n                          newSlots[index] = e.target.value;\n                          setClinicForm({\n                            ...clinicForm,\n                            availableSlots: newSlots\n                          });\n                        },\n                        className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                        required: true\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1542,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: () => {\n                          const newSlots = [...clinicForm.availableSlots];\n                          newSlots.splice(index, 1);\n                          setClinicForm({\n                            ...clinicForm,\n                            availableSlots: newSlots\n                          });\n                        },\n                        className: \"text-red-500 hover:text-red-700\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          className: \"h-5 w-5\",\n                          viewBox: \"0 0 20 20\",\n                          fill: \"currentColor\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z\",\n                            clipRule: \"evenodd\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1563,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1562,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1553,\n                        columnNumber: 29\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1541,\n                      columnNumber: 27\n                    }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => {\n                        setClinicForm({\n                          ...clinicForm,\n                          availableSlots: [...clinicForm.availableSlots, '09:00']\n                        });\n                      },\n                      className: \"px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors w-full\",\n                      children: \"+ Add Time Slot\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1568,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1539,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1537,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1521,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1519,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Working Hours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1586,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2 p-4 border border-gray-200 rounded-lg\",\n                children: Object.entries(clinicForm.workingHours).map(([day, hours]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-xs font-medium text-gray-700 mb-1 capitalize\",\n                    children: day\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1590,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: hours,\n                    onChange: e => setClinicForm({\n                      ...clinicForm,\n                      workingHours: {\n                        ...clinicForm.workingHours,\n                        [day]: e.target.value\n                      }\n                    }),\n                    placeholder: \"09:00-17:00 or Closed\",\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1591,\n                    columnNumber: 25\n                  }, this)]\n                }, day, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1589,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1587,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1585,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: [\"Address \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-400 text-xs\",\n                  children: \"(Only City is required)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1610,\n                  columnNumber: 91\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1610,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-xs font-medium text-gray-700 mb-1\",\n                    children: \"Street (English)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1613,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: clinicForm.address.street.en,\n                    onChange: e => setClinicForm({\n                      ...clinicForm,\n                      address: {\n                        ...clinicForm.address,\n                        street: {\n                          ...clinicForm.address.street,\n                          en: e.target.value\n                        }\n                      }\n                    }),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1614,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1612,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-xs font-medium text-gray-700 mb-1\",\n                    children: \"Street (Arabic)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1631,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: clinicForm.address.street.ar,\n                    onChange: e => setClinicForm({\n                      ...clinicForm,\n                      address: {\n                        ...clinicForm.address,\n                        street: {\n                          ...clinicForm.address.street,\n                          ar: e.target.value\n                        }\n                      }\n                    }),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1632,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1630,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-xs font-medium text-gray-700 mb-1\",\n                    children: \"Country (English)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1649,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: clinicForm.address.country.en,\n                    onChange: e => setClinicForm({\n                      ...clinicForm,\n                      address: {\n                        ...clinicForm.address,\n                        country: {\n                          ...clinicForm.address.country,\n                          en: e.target.value\n                        }\n                      }\n                    }),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1650,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1648,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-xs font-medium text-gray-700 mb-1\",\n                    children: \"Country (Arabic)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1667,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: clinicForm.address.country.ar,\n                    onChange: e => setClinicForm({\n                      ...clinicForm,\n                      address: {\n                        ...clinicForm.address,\n                        country: {\n                          ...clinicForm.address.country,\n                          ar: e.target.value\n                        }\n                      }\n                    }),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1668,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1666,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-xs font-medium text-gray-700 mb-1\",\n                    children: \"Postal Code\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1685,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: clinicForm.address.postalCode,\n                    onChange: e => setClinicForm({\n                      ...clinicForm,\n                      address: {\n                        ...clinicForm.address,\n                        postalCode: e.target.value\n                      }\n                    }),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1686,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1684,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1611,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1609,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-4 pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                type: \"button\",\n                onClick: () => {\n                  setShowClinicModal(false);\n                  navigate('/superadmin/dashboard', {\n                    state: {}\n                  });\n                },\n                className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\",\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1703,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                type: \"submit\",\n                className: \"px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 font-medium transition-colors shadow-md hover:shadow-lg\",\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: editClinic ? 'Update Clinic' : 'Add Clinic'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1712,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1702,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1325,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1316,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1311,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1310,\n      columnNumber: 9\n    }, this), showAccountModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-blue-900\",\n              children: \"Add Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1736,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowAccountModal(false),\n              className: \"text-gray-400 hover:text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1739,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1738,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1737,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1735,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleAccountSubmit,\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Email*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1746,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  value: accountForm.email,\n                  onChange: e => setAccountForm({\n                    ...accountForm,\n                    email: e.target.value\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1747,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1745,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Password*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1757,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"password\",\n                  name: \"password\",\n                  value: accountForm.password,\n                  onChange: e => setAccountForm({\n                    ...accountForm,\n                    password: e.target.value\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true,\n                  minLength: \"6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1758,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 mt-1\",\n                  children: \"Password must be at least 6 characters long\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1767,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1756,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Name*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1770,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"name\",\n                  value: accountForm.name,\n                  onChange: e => setAccountForm({\n                    ...accountForm,\n                    name: e.target.value\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1771,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1769,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Role*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1781,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"role\",\n                  value: accountForm.role,\n                  onChange: e => setAccountForm({\n                    ...accountForm,\n                    role: e.target.value\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"student\",\n                    children: \"Student\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1789,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"supervisor\",\n                    children: \"Supervisor\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1790,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"admin\",\n                    children: \"Admin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1791,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"assistant\",\n                    children: \"Assistant\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1792,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"dentist\",\n                    children: \"Dentist\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1793,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1782,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1780,\n                columnNumber: 19\n              }, this), accountForm.role === 'student' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Student ID*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1798,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"studentId\",\n                  value: accountForm.studentId,\n                  onChange: e => setAccountForm({\n                    ...accountForm,\n                    studentId: e.target.value\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1799,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1797,\n                columnNumber: 21\n              }, this), ['student', 'supervisor', 'admin', 'assistant'].includes(accountForm.role) && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"University*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1811,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"universityId\",\n                  value: accountForm.universityId,\n                  onChange: e => setAccountForm({\n                    ...accountForm,\n                    universityId: e.target.value\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select University\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1819,\n                    columnNumber: 25\n                  }, this), universities.map(university => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: university.universityId,\n                    children: university.name.en\n                  }, university.universityId, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1821,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1812,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1810,\n                columnNumber: 21\n              }, this), accountForm.role === 'assistant' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Dentist*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1830,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"dentistId\",\n                  value: accountForm.dentistId,\n                  onChange: e => setAccountForm({\n                    ...accountForm,\n                    dentistId: e.target.value\n                  }),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Dentist\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1838,\n                    columnNumber: 25\n                  }, this), clinics.map(clinic => {\n                    var _clinic$name;\n                    return /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: clinic.dentistId,\n                      children: ((_clinic$name = clinic.name) === null || _clinic$name === void 0 ? void 0 : _clinic$name.en) || clinic.dentistId\n                    }, clinic.dentistId, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1840,\n                      columnNumber: 27\n                    }, this);\n                  })]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1831,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1829,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1744,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-4 pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                type: \"button\",\n                onClick: () => setShowAccountModal(false),\n                className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\",\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1849,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                type: \"submit\",\n                className: \"px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 font-medium transition-colors shadow-md hover:shadow-lg\",\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: \"Add Account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1858,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1848,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1743,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1734,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1729,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1728,\n      columnNumber: 9\n    }, this), showNewsModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-blue-900\",\n              children: \"Send News\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1882,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowNewsModal(false),\n              className: \"text-gray-400 hover:text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1885,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1884,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1883,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1881,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleNewsSubmit,\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Title*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1891,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: newsForm.title,\n                onChange: e => setNewsForm({\n                  ...newsForm,\n                  title: e.target.value\n                }),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1892,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1890,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Content*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1901,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: newsForm.content,\n                onChange: e => setNewsForm({\n                  ...newsForm,\n                  content: e.target.value\n                }),\n                rows: \"4\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1902,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1900,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: \"isGlobal\",\n                checked: newsForm.isGlobal,\n                onChange: e => setNewsForm({\n                  ...newsForm,\n                  isGlobal: e.target.checked\n                }),\n                className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1911,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"isGlobal\",\n                className: \"ml-2 block text-sm text-gray-900\",\n                children: \"Global Announcement (visible to all universities)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1918,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1910,\n              columnNumber: 17\n            }, this), !newsForm.isGlobal && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"University ID*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1924,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: newsForm.university,\n                onChange: e => setNewsForm({\n                  ...newsForm,\n                  university: e.target.value\n                }),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                required: !newsForm.isGlobal,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select University\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1931,\n                  columnNumber: 23\n                }, this), universities.map(university => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: university.universityId,\n                  children: university.name.en\n                }, university.universityId, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1933,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1925,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1923,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-4 pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                type: \"button\",\n                onClick: () => setShowNewsModal(false),\n                className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\",\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1941,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                type: \"submit\",\n                className: \"px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 font-medium transition-colors shadow-md hover:shadow-lg\",\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: \"Send News\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1950,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1940,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1889,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1880,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1875,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1874,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(SuccessModal, {\n      isOpen: showSuccessModal,\n      onClose: () => setShowSuccessModal(false),\n      title: successTitle,\n      message: successMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1966,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 602,\n    columnNumber: 5\n  }, this);\n};\n_s(SuperAdminDashboard, \"25lWBgzu4Pyzvs/H1oWg0l5+odM=\", false, function () {\n  return [useNavigate, useAuth, useLocation];\n});\n_c = SuperAdminDashboard;\nexport default SuperAdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"SuperAdminDashboard\");", "map": {"version": 3, "names": ["useState", "useEffect", "useNavigate", "useLocation", "axios", "motion", "FaUniversity", "FaUserPlus", "FaNewspaper", "FaUsers", "<PERSON><PERSON><PERSON>", "Loader", "SuccessModal", "useAuth", "SuperAdminSidebar", "jsxDEV", "_jsxDEV", "SuperAdminDashboard", "_s", "sidebarOpen", "setSidebarOpen", "showUniversityModal", "setShowUniversityModal", "showClinicModal", "setShowClinicModal", "showAccountModal", "setShowAccountModal", "showNewsModal", "setShowNewsModal", "showSuccessModal", "setShowSuccessModal", "successMessage", "setSuccessMessage", "successTitle", "setSuccessTitle", "universities", "setUniversities", "clinics", "setClinics", "analytics", "setAnalytics", "totalUniversities", "totalClinics", "totalAccounts", "recentActivity", "loading", "setLoading", "error", "setError", "navigate", "user", "token", "location", "editUniversity", "editClinic", "state", "universityForm", "setUniversityForm", "processedUniversity", "slotBeginDate", "Date", "toISOString", "split", "slotEndDate", "Array", "isArray", "timeSlots", "length", "availableSlots", "Set", "map", "slot", "time", "universityId", "name", "en", "ar", "description", "dentistryInfo", "facilities", "program", "dentistryServices", "address", "street", "city", "country", "postalCode", "contactInfo", "phone", "email", "website", "image", "logo", "mapUrl", "setFullYear", "getFullYear", "slotDuration", "holidays", "clinicForm", "setClinicForm", "processedClinic", "services", "dentistId", "clinicName", "about", "workingHours", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday", "accountForm", "setAccountForm", "password", "role", "studentId", "newsForm", "setNewsForm", "title", "content", "isGlobal", "university", "fetchData", "config", "headers", "Authorization", "universitiesRes", "clinicsRes", "analyticsRes", "Promise", "all", "get", "process", "env", "REACT_APP_API_URL", "data", "err", "_err$response", "_err$response2", "_err$response3", "_err$response3$data", "_err$response4", "console", "response", "message", "errorMessage", "status", "handleUniversitySubmit", "e", "preventDefault", "trim", "formToSubmit", "for<PERSON>ach", "field", "service", "day", "char<PERSON>t", "toUpperCase", "slice", "toLowerCase", "put", "u", "post", "_err$response5", "_err$response5$data", "_err$response6", "_err$response6$data", "handleClinicSubmit", "randomId", "Math", "floor", "random", "log", "emailRegex", "test", "days", "c", "_err$response7", "_err$response8", "_err$response9", "_err$response10", "_err$response10$data", "_err$response11", "_err$response11$data", "handleAccountSubmit", "includes", "_err$response12", "_err$response12$data", "handleNewsSubmit", "_err$response13", "_err$response13$data", "container", "hidden", "opacity", "show", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "item", "y", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "isOpen", "setIsOpen", "toggleSidebar", "div", "initial", "animate", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "duration", "button", "whileHover", "scale", "whileTap", "onClick", "variants", "whileInView", "viewport", "once", "stopPropagation", "delay", "colSpan", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "activity", "index", "tr", "action", "date", "toLocaleDateString", "weekday", "month", "onSubmit", "type", "value", "onChange", "target", "required", "disabled", "rows", "newServices", "splice", "parseInt", "min", "step", "newSlots", "id", "checked", "currentHolidays", "filter", "holiday", "htmlFor", "Object", "entries", "hours", "placeholder", "<PERSON><PERSON><PERSON><PERSON>", "clinic", "_clinic$name", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/superadmin/Dashboard.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport { useNavigate, useLocation } from 'react-router-dom';\r\nimport axios from 'axios';\r\nimport { motion } from 'framer-motion';\r\nimport { FaUniversity, FaUserPlus, FaNewspaper, FaUsers } from 'react-icons/fa';\r\nimport Navbar from '../student/Navbar';\r\nimport Loader from '../components/Loader';\r\nimport SuccessModal from '../components/SuccessModal';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport SuperAdminSidebar from './SuperAdminSidebar';\r\n\r\nconst SuperAdminDashboard = () => {\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [showUniversityModal, setShowUniversityModal] = useState(false);\r\n  const [showClinicModal, setShowClinicModal] = useState(false);\r\n  const [showAccountModal, setShowAccountModal] = useState(false);\r\n  const [showNewsModal, setShowNewsModal] = useState(false);\r\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [successTitle, setSuccessTitle] = useState('Success');\r\n  const [universities, setUniversities] = useState([]);\r\n  const [clinics, setClinics] = useState([]);\r\n  const [analytics, setAnalytics] = useState({ totalUniversities: 0, totalClinics: 0, totalAccounts: 0, recentActivity: [] });\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const navigate = useNavigate();\r\n  const { user, token } = useAuth();\r\n  const location = useLocation();\r\n  const { editUniversity, editClinic } = location.state || {};\r\n\r\n  const [universityForm, setUniversityForm] = useState(() => {\r\n    // If we're editing an existing university\r\n    if (editUniversity) {\r\n      // Process the university data for editing\r\n      const processedUniversity = { ...editUniversity };\r\n\r\n      // Format dates properly\r\n      if (processedUniversity.slotBeginDate) {\r\n        processedUniversity.slotBeginDate = new Date(processedUniversity.slotBeginDate).toISOString().split('T')[0];\r\n      }\r\n\r\n      if (processedUniversity.slotEndDate) {\r\n        processedUniversity.slotEndDate = new Date(processedUniversity.slotEndDate).toISOString().split('T')[0];\r\n      }\r\n\r\n      // Extract available slots from timeSlots if they exist\r\n      if (Array.isArray(processedUniversity.timeSlots) && processedUniversity.timeSlots.length > 0) {\r\n        // Get unique time slots\r\n        processedUniversity.availableSlots = [...new Set(processedUniversity.timeSlots.map(slot => slot.time))];\r\n      } else {\r\n        processedUniversity.availableSlots = ['09:00', '11:30', '14:00']; // Default\r\n      }\r\n\r\n      return processedUniversity;\r\n    } else {\r\n      // Default values for a new university\r\n      return {\r\n        universityId: '',\r\n        name: { en: '', ar: '' },\r\n        description: { en: '', ar: '' },\r\n        dentistryInfo: { en: '', ar: '' },\r\n        facilities: { en: '', ar: '' },\r\n        program: { en: '', ar: '' },\r\n        dentistryServices: [{ en: '', ar: '' }],\r\n        address: {\r\n          street: { en: '', ar: '' },\r\n          city: { en: '', ar: '' },\r\n          country: { en: '', ar: '' },\r\n          postalCode: '',\r\n        },\r\n        contactInfo: {\r\n          phone: '',\r\n          email: '',\r\n          website: '',\r\n        },\r\n        image: '',\r\n        logo: '',\r\n        mapUrl: '',\r\n        slotBeginDate: new Date().toISOString().split('T')[0],\r\n        slotEndDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],\r\n        slotDuration: 120, // Default slot duration in minutes\r\n        availableSlots: ['09:00', '11:30', '14:00'], // Default available time slots\r\n        holidays: ['Friday', 'Sunday'], // Default holidays\r\n      };\r\n    }\r\n  });\r\n\r\n  const [clinicForm, setClinicForm] = useState(() => {\r\n    // If we're editing an existing clinic\r\n    if (editClinic) {\r\n      // Process the clinic data for editing\r\n      const processedClinic = { ...editClinic };\r\n\r\n      // Format dates properly\r\n      if (processedClinic.slotBeginDate) {\r\n        processedClinic.slotBeginDate = new Date(processedClinic.slotBeginDate).toISOString().split('T')[0];\r\n      }\r\n\r\n      if (processedClinic.slotEndDate) {\r\n        processedClinic.slotEndDate = new Date(processedClinic.slotEndDate).toISOString().split('T')[0];\r\n      }\r\n\r\n      // Extract available slots from timeSlots if they exist\r\n      if (Array.isArray(processedClinic.timeSlots) && processedClinic.timeSlots.length > 0) {\r\n        // Get unique time slots\r\n        processedClinic.availableSlots = [...new Set(processedClinic.timeSlots.map(slot => slot.time))];\r\n      } else {\r\n        processedClinic.availableSlots = ['09:00', '11:00', '13:00', '15:00']; // Default\r\n      }\r\n\r\n      // Ensure services is an array\r\n      if (!processedClinic.services || !Array.isArray(processedClinic.services)) {\r\n        processedClinic.services = [];\r\n      }\r\n\r\n      return processedClinic;\r\n    } else {\r\n      // Default values for a new clinic\r\n      return {\r\n        dentistId: '',\r\n        name: { en: '', ar: '' },\r\n        clinicName: { en: '', ar: '' },\r\n        about: { en: '', ar: '' },\r\n        services: [],\r\n        address: {\r\n          street: { en: '', ar: '' },\r\n          city: { en: '', ar: '' },\r\n          country: { en: '', ar: '' },\r\n          postalCode: '',\r\n        },\r\n        contactInfo: { phone: '', email: '', website: '' },\r\n        slotBeginDate: new Date().toISOString().split('T')[0],\r\n        slotEndDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],\r\n        slotDuration: 60, // Default slot duration in minutes\r\n        availableSlots: ['09:00', '11:00', '13:00', '15:00'], // Default available time slots\r\n        workingHours: {\r\n          monday: '09:00-17:00',\r\n          tuesday: '09:00-17:00',\r\n          wednesday: '09:00-17:00',\r\n          thursday: '09:00-17:00',\r\n          friday: 'Closed',\r\n          saturday: '09:00-13:00',\r\n          sunday: 'Closed',\r\n        },\r\n        holidays: ['Friday', 'Sunday'], // Default holidays\r\n      };\r\n    }\r\n  });\r\n\r\n  const [accountForm, setAccountForm] = useState({\r\n    email: '',\r\n    password: '',\r\n    name: '',\r\n    role: 'student',\r\n    studentId: '',\r\n    universityId: '',\r\n    dentistId: '',\r\n  });\r\n\r\n  const [newsForm, setNewsForm] = useState({\r\n    title: '',\r\n    content: '',\r\n    isGlobal: true,\r\n    university: '',\r\n  });\r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      if (!user || !token) {\r\n        setError('Please log in to view your dashboard.');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const config = { headers: { Authorization: `Bearer ${token}` } };\r\n        // Fetch all data in parallel for better performance\r\n        const [universitiesRes, clinicsRes, analyticsRes] = await Promise.all([\r\n          axios.get(`${process.env.REACT_APP_API_URL}/api/universities`, config),\r\n          axios.get(`${process.env.REACT_APP_API_URL}/api/dentists`, config),\r\n          axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/superadmin`, config),\r\n        ]);\r\n\r\n        setUniversities(universitiesRes.data || []);\r\n        setClinics(clinicsRes.data || []);\r\n        setAnalytics(analyticsRes.data || { totalUniversities: 0, totalClinics: 0, totalAccounts: 0, recentActivity: [] });\r\n      } catch (err) {\r\n        console.error('Fetch error:', err.response?.data || err.message);\r\n        const errorMessage = err.response?.status === 401\r\n          ? 'Unauthorized. Please log in again.'\r\n          : err.response?.data?.message || 'Failed to load data';\r\n        setError(errorMessage);\r\n        if (err.response?.status === 401) {\r\n          navigate('/login');\r\n        }\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    fetchData();\r\n\r\n    if (editUniversity) setShowUniversityModal(true);\r\n    if (editClinic) setShowClinicModal(true);\r\n  }, [user, token, navigate, editUniversity, editClinic]);\r\n\r\n  const handleUniversitySubmit = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n      // Validate required fields on the frontend\r\n      if (!universityForm.universityId.trim()) {\r\n        setError('University ID is required');\r\n        return;\r\n      }\r\n\r\n      if (!universityForm.name.en.trim() || !universityForm.name.ar.trim()) {\r\n        setError('University name is required in both English and Arabic');\r\n        return;\r\n      }\r\n\r\n      if (!universityForm.contactInfo.phone.trim() || !universityForm.contactInfo.email.trim()) {\r\n        setError('Contact information (phone and email) is required');\r\n        return;\r\n      }\r\n\r\n      // Ensure all bilingual fields have at least placeholder values\r\n      const formToSubmit = { ...universityForm };\r\n\r\n      // Set default values for empty bilingual fields\r\n      ['description', 'dentistryInfo', 'facilities', 'program'].forEach(field => {\r\n        if (!formToSubmit[field].en.trim() || !formToSubmit[field].ar.trim()) {\r\n          formToSubmit[field] = {\r\n            en: formToSubmit[field].en.trim() || 'Not provided',\r\n            ar: formToSubmit[field].ar.trim() || 'غير متوفر'\r\n          };\r\n        }\r\n      });\r\n\r\n      // Ensure dentistry services have values\r\n      if (formToSubmit.dentistryServices.length === 0) {\r\n        formToSubmit.dentistryServices = [{ en: 'General Dentistry', ar: 'طب الأسنان العام' }];\r\n      } else {\r\n        formToSubmit.dentistryServices = formToSubmit.dentistryServices.map(service => ({\r\n          en: service.en.trim() || 'General Dentistry',\r\n          ar: service.ar.trim() || 'طب الأسنان العام'\r\n        }));\r\n      }\r\n\r\n      // Make sure holidays are properly formatted and not empty\r\n      if (!formToSubmit.holidays || !Array.isArray(formToSubmit.holidays) || formToSubmit.holidays.length === 0) {\r\n        // If holidays array is empty or not defined, set default values\r\n        formToSubmit.holidays = [\"Friday\", \"Sunday\"];\r\n      } else {\r\n        // Ensure day names are properly capitalized\r\n        formToSubmit.holidays = formToSubmit.holidays.map(day => {\r\n          // Capitalize first letter\r\n          return day.charAt(0).toUpperCase() + day.slice(1).toLowerCase();\r\n        });\r\n      }\r\n\r\n      const config = { headers: { Authorization: `Bearer ${token}` } };\r\n      if (editUniversity) {\r\n        const response = await axios.put(`http://localhost:5000/api/universities/${formToSubmit.universityId}`, formToSubmit, config);\r\n        setUniversities(universities.map(u => u.universityId === formToSubmit.universityId ? response.data : u));\r\n        setSuccessTitle('University Updated');\r\n        setSuccessMessage('University has been updated successfully!');\r\n        setShowSuccessModal(true);\r\n      } else {\r\n        const response = await axios.post('http://localhost:5000/api/universities', formToSubmit, config);\r\n        setUniversities([...universities, response.data]);\r\n        setSuccessTitle('University Added');\r\n        setSuccessMessage('University has been added successfully!');\r\n        setShowSuccessModal(true);\r\n      }\r\n      setShowUniversityModal(false);\r\n      setUniversityForm({\r\n        universityId: '',\r\n        name: { en: '', ar: '' },\r\n        description: { en: '', ar: '' },\r\n        dentistryInfo: { en: '', ar: '' },\r\n        facilities: { en: '', ar: '' },\r\n        program: { en: '', ar: '' },\r\n        dentistryServices: [{ en: '', ar: '' }],\r\n        address: {\r\n          street: { en: '', ar: '' },\r\n          city: { en: '', ar: '' },\r\n          country: { en: '', ar: '' },\r\n          postalCode: '',\r\n        },\r\n        contactInfo: {\r\n          phone: '',\r\n          email: '',\r\n          website: '',\r\n        },\r\n        image: '',\r\n        logo: '',\r\n        mapUrl: '',\r\n        slotBeginDate: new Date().toISOString().split('T')[0],\r\n        slotEndDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],\r\n        slotDuration: 120, // Default slot duration in minutes\r\n        availableSlots: ['09:00', '11:30', '14:00'], // Default available time slots\r\n        holidays: ['Friday', 'Sunday'], // Default holidays\r\n      });\r\n\r\n      // Refresh analytics to update university count\r\n      const analyticsRes = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/superadmin`, config);\r\n      setAnalytics(analyticsRes.data || { totalUniversities: 0, totalClinics: 0, totalAccounts: 0, recentActivity: [] });\r\n\r\n      navigate('/superadmin/dashboard', { state: {} });\r\n    } catch (err) {\r\n      console.error('University submission error:', err);\r\n\r\n      // Create a more detailed error message\r\n      let errorMessage = 'Failed to save university. ';\r\n\r\n      if (err.response?.data?.error) {\r\n        // If the backend sends a detailed error message\r\n        errorMessage += err.response.data.error;\r\n      } else if (err.response?.data?.message) {\r\n        // If the backend sends a simple message\r\n        errorMessage += err.response.data.message;\r\n      } else if (err.message) {\r\n        // If there's a general error message\r\n        errorMessage += err.message;\r\n      }\r\n\r\n      setError(errorMessage);\r\n    }\r\n  };\r\n\r\n  const handleClinicSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setError('');\r\n    try {\r\n      // Auto-generate dentistId if not provided and not editing\r\n      if (!editClinic && !clinicForm.dentistId.trim()) {\r\n        // Generate a unique ID with 'D' prefix and a random number\r\n        const randomId = 'D' + Math.floor(100000 + Math.random() * 900000);\r\n        clinicForm.dentistId = randomId;\r\n        console.log('Auto-generated dentistId:', randomId);\r\n      } else if (editClinic && !clinicForm.dentistId.trim()) {\r\n        setError('Dentist ID is required');\r\n        return;\r\n      }\r\n\r\n      if (!clinicForm.name.en.trim() || !clinicForm.name.ar.trim()) {\r\n        setError('Dentist name is required in both English and Arabic');\r\n        return;\r\n      }\r\n\r\n      if (!clinicForm.clinicName.en.trim() || !clinicForm.clinicName.ar.trim()) {\r\n        setError('Clinic name is required in both English and Arabic');\r\n        return;\r\n      }\r\n\r\n      if (!clinicForm.contactInfo.phone.trim()) {\r\n        setError('Phone number is required');\r\n        return;\r\n      }\r\n\r\n      if (!clinicForm.contactInfo.email || !clinicForm.contactInfo.email.trim()) {\r\n        setError('Email is required');\r\n        return;\r\n      }\r\n\r\n      // Validate email format\r\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n      if (!emailRegex.test(clinicForm.contactInfo.email.trim())) {\r\n        setError('Please enter a valid email address');\r\n        return;\r\n      }\r\n\r\n      // Only validate city as required in address fields\r\n      if (!clinicForm.address.city.en.trim() || !clinicForm.address.city.ar.trim()) {\r\n        setError('City is required in both English and Arabic');\r\n        return;\r\n      }\r\n\r\n      if (!clinicForm.slotBeginDate || !clinicForm.slotEndDate) {\r\n        setError('Slot begin and end dates are required');\r\n        return;\r\n      }\r\n\r\n      if (!clinicForm.slotDuration || clinicForm.slotDuration < 30) {\r\n        setError('Valid slot duration is required (minimum 30 minutes)');\r\n        return;\r\n      }\r\n\r\n      if (!clinicForm.availableSlots || clinicForm.availableSlots.length === 0) {\r\n        setError('At least one available time slot is required');\r\n        return;\r\n      }\r\n\r\n      // Create a copy of the form data to modify\r\n      const formToSubmit = { ...clinicForm };\r\n\r\n      // Add email at the root level to match the schema\r\n      formToSubmit.email = formToSubmit.contactInfo.email.trim();\r\n\r\n      // Ensure all required bilingual fields have values\r\n      ['about'].forEach(field => {\r\n        if (!formToSubmit[field].en.trim() || !formToSubmit[field].ar.trim()) {\r\n          formToSubmit[field] = {\r\n            en: formToSubmit[field].en.trim() || 'Not provided',\r\n            ar: formToSubmit[field].ar.trim() || 'غير متوفر'\r\n          };\r\n        }\r\n      });\r\n\r\n      // Ensure services have values\r\n      if (!formToSubmit.services || formToSubmit.services.length === 0) {\r\n        formToSubmit.services = [{ en: 'General Dentistry', ar: 'طب الأسنان العام' }];\r\n      } else {\r\n        formToSubmit.services = formToSubmit.services.map(service => ({\r\n          en: service.en.trim() || 'General Dentistry',\r\n          ar: service.ar.trim() || 'طب الأسنان العام'\r\n        }));\r\n      }\r\n\r\n      // Make sure holidays are properly formatted and not empty\r\n      if (!formToSubmit.holidays || !Array.isArray(formToSubmit.holidays) || formToSubmit.holidays.length === 0) {\r\n        // If holidays array is empty or not defined, set default values\r\n        formToSubmit.holidays = [\"Friday\", \"Sunday\"];\r\n      } else {\r\n        // Ensure day names are properly capitalized\r\n        formToSubmit.holidays = formToSubmit.holidays.map(day => {\r\n          // Capitalize first letter\r\n          return day.charAt(0).toUpperCase() + day.slice(1).toLowerCase();\r\n        });\r\n      }\r\n\r\n      // Ensure working hours are properly formatted\r\n      const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];\r\n      days.forEach(day => {\r\n        if (!formToSubmit.workingHours[day]) {\r\n          formToSubmit.workingHours[day] = 'Closed';\r\n        }\r\n      });\r\n\r\n      const config = { headers: { Authorization: `Bearer ${token}` } };\r\n\r\n      console.log('Submitting clinic data:', formToSubmit);\r\n\r\n      if (editClinic) {\r\n        const response = await axios.put(`${process.env.REACT_APP_API_URL}/api/dentists/${formToSubmit.dentistId}`, formToSubmit, config);\r\n        setClinics(clinics.map(c => c.dentistId === formToSubmit.dentistId ? response.data : c));\r\n        setSuccessTitle('Clinic Updated');\r\n        setSuccessMessage('Clinic has been updated successfully!');\r\n        setShowSuccessModal(true);\r\n      } else {\r\n        const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/dentists`, formToSubmit, config);\r\n        setClinics([...clinics, response.data]);\r\n        setSuccessTitle('Clinic Added');\r\n        setSuccessMessage('Clinic has been added successfully!');\r\n        setShowSuccessModal(true);\r\n      }\r\n      setShowClinicModal(false);\r\n      setClinicForm({\r\n        dentistId: '',\r\n        name: { en: '', ar: '' },\r\n        clinicName: { en: '', ar: '' },\r\n        about: { en: '', ar: '' },\r\n        services: [],\r\n        address: {\r\n          street: { en: '', ar: '' },\r\n          city: { en: '', ar: '' },\r\n          country: { en: '', ar: '' },\r\n          postalCode: '',\r\n        },\r\n        contactInfo: { phone: '', email: '', website: '' },\r\n        slotBeginDate: new Date().toISOString().split('T')[0],\r\n        slotEndDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],\r\n        slotDuration: 60, // Default slot duration in minutes\r\n        availableSlots: ['09:00', '11:00', '13:00', '15:00'], // Default available time slots\r\n        workingHours: {\r\n          monday: '09:00-17:00',\r\n          tuesday: '09:00-17:00',\r\n          wednesday: '09:00-17:00',\r\n          thursday: '09:00-17:00',\r\n          friday: 'Closed',\r\n          saturday: '09:00-13:00',\r\n          sunday: 'Closed',\r\n        },\r\n        holidays: ['Friday', 'Sunday'], // Default holidays\r\n      });\r\n\r\n      // Refresh analytics to update clinic count\r\n      const analyticsRes = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/superadmin`, config);\r\n      setAnalytics(analyticsRes.data || { totalUniversities: 0, totalClinics: 0, totalAccounts: 0, recentActivity: [] });\r\n\r\n      navigate('/superadmin/dashboard', { state: {} });\r\n    } catch (err) {\r\n      console.error('Clinic submission error:', err);\r\n\r\n      // Log detailed error information for debugging\r\n      if (err.response?.data) {\r\n        console.error('Server response data:', err.response.data);\r\n      }\r\n      if (err.response?.status) {\r\n        console.error('Server response status:', err.response.status);\r\n      }\r\n      if (err.response?.headers) {\r\n        console.error('Server response headers:', err.response.headers);\r\n      }\r\n\r\n      // Create a detailed error message\r\n      let errorMessage = 'Failed to save clinic. ';\r\n\r\n      if (err.response?.data?.error) {\r\n        // If the backend sends a detailed error message\r\n        errorMessage += err.response.data.error;\r\n      } else if (err.response?.data?.message) {\r\n        // If the backend sends a simple message\r\n        errorMessage += err.response.data.message;\r\n      } else if (err.message) {\r\n        // If there's a general error message\r\n        errorMessage += err.message;\r\n      }\r\n\r\n      setError(errorMessage);\r\n    }\r\n  };\r\n\r\n  const handleAccountSubmit = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n      // Validate form based on role\r\n      if (accountForm.role === 'student' && !accountForm.studentId) {\r\n        setError('Student ID is required for student accounts');\r\n        return;\r\n      }\r\n\r\n      if (['student', 'supervisor', 'admin', 'assistant'].includes(accountForm.role) && !accountForm.universityId) {\r\n        setError('University ID is required for this role');\r\n        return;\r\n      }\r\n\r\n      if (accountForm.role === 'assistant' && !accountForm.dentistId) {\r\n        setError('Dentist ID is required for assistant accounts');\r\n        return;\r\n      }\r\n\r\n      const config = { headers: { Authorization: `Bearer ${token}` } };\r\n      await axios.post('http://localhost:5000/api/accounts', accountForm, config);\r\n\r\n      setShowAccountModal(false);\r\n      setAccountForm({\r\n        email: '',\r\n        password: '',\r\n        name: '',\r\n        role: 'student',\r\n        studentId: '',\r\n        universityId: '',\r\n        dentistId: '',\r\n      });\r\n\r\n      // Show success message\r\n      setSuccessTitle('Account Created');\r\n      setSuccessMessage('Account has been created successfully!');\r\n      setShowSuccessModal(true);\r\n\r\n      // Refresh analytics to update account count\r\n      const analyticsRes = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/superadmin`, config);\r\n      setAnalytics(analyticsRes.data || { totalUniversities: 0, totalClinics: 0, totalAccounts: 0, recentActivity: [] });\r\n    } catch (err) {\r\n      console.error('Account creation error:', err);\r\n      setError(err.response?.data?.message || 'Failed to add account');\r\n    }\r\n  };\r\n\r\n  const handleNewsSubmit = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n      const config = { headers: { Authorization: `Bearer ${token}` } };\r\n      await axios.post('http://localhost:5000/api/news', newsForm, config);\r\n      setShowNewsModal(false);\r\n      setNewsForm({ title: '', content: '', isGlobal: true, university: '' });\r\n      // Show success message\r\n      setSuccessTitle('News Sent');\r\n      setSuccessMessage('News has been sent successfully!');\r\n      setShowSuccessModal(true);\r\n    } catch (err) {\r\n      console.error('News submission error:', err);\r\n      setError(err.response?.data?.message || 'Failed to send news');\r\n    }\r\n  };\r\n\r\n  const container = {\r\n    hidden: { opacity: 0 },\r\n    show: { opacity: 1, transition: { staggerChildren: 0.1 } },\r\n  };\r\n\r\n  const item = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    show: { opacity: 1, y: 0 },\r\n  };\r\n\r\n  if (loading) {\r\n    return <Loader />;\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex h-screen bg-gray-50\">\r\n      <SuperAdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\r\n\r\n        <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\">\r\n          <div className=\"max-w-7xl mx-auto\">\r\n            {error && (\r\n              <motion.div\r\n                initial={{ opacity: 0, y: -20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                className=\"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\"\r\n              >\r\n                <div className=\"flex items-center\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-red-500 mr-3\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                    <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                  <p className=\"text-red-700 font-medium\">{error}</p>\r\n                </div>\r\n              </motion.div>\r\n            )}\r\n\r\n            <motion.div\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              transition={{ duration: 0.5 }}\r\n            >\r\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\r\n                <div>\r\n                  <h1 className=\"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\">\r\n                    Super Admin Dashboard\r\n                  </h1>\r\n                  <p className=\"text-[#333333]\">Welcome back, {user?.name || 'Super Admin'}</p>\r\n                </div>\r\n                <div className=\"flex flex-col sm:flex-row gap-3 w-full sm:w-auto\">\r\n                  <motion.button\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                    onClick={() => setShowUniversityModal(true)}\r\n                    className=\"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\"\r\n                  >\r\n                    <FaUniversity className=\"h-5 w-5 mr-2\" />\r\n                    Add University\r\n                  </motion.button>\r\n                  <motion.button\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                    onClick={() => setShowAccountModal(true)}\r\n                    className=\"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\"\r\n                  >\r\n                    <FaUserPlus className=\"h-5 w-5 mr-2\" />\r\n                    Add Account\r\n                  </motion.button>\r\n                </div>\r\n              </div>\r\n\r\n              <motion.div\r\n                variants={container}\r\n                initial=\"hidden\"\r\n                whileInView=\"show\"\r\n                viewport={{ once: true }}\r\n                className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\"\r\n              >\r\n                <motion.div\r\n                  variants={item}\r\n                  className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#0077B6] group cursor-pointer\"\r\n                  onClick={() => navigate('/superadmin/universities')}\r\n                >\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"bg-[rgba(0,119,182,0.1)] w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-[rgba(0,119,182,0.2)] transition-colors duration-300\">\r\n                      <FaUniversity className=\"h-6 w-6 text-[#0077B6]\" />\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"text-sm font-medium text-gray-500\">Total Universities</p>\r\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{analytics.totalUniversities}</p>\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  variants={item}\r\n                  className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#0077B6] group cursor-pointer\"\r\n                  onClick={() => navigate('/superadmin/accounts')}\r\n                >\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"bg-[rgba(0,119,182,0.1)] w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-[rgba(0,119,182,0.2)] transition-colors duration-300\">\r\n                      <FaUsers className=\"h-6 w-6 text-[#0077B6]\" />\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"text-sm font-medium text-gray-500\">Total Accounts</p>\r\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{analytics.totalAccounts}</p>\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  variants={item}\r\n                  className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#0077B6] group cursor-pointer\"\r\n                  onClick={() => navigate('/superadmin/news')}\r\n                >\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"bg-[rgba(0,119,182,0.1)] w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-[rgba(0,119,182,0.2)] transition-colors duration-300\">\r\n                      <FaNewspaper className=\"h-6 w-6 text-[#0077B6]\" />\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"text-sm font-medium text-gray-500\">News</p>\r\n                      <button\r\n                        onClick={(e) => {\r\n                          e.stopPropagation(); // Prevent navigation when clicking the button\r\n                          setShowNewsModal(true);\r\n                        }}\r\n                        className=\"text-[#0077B6] hover:underline text-sm font-medium\"\r\n                      >\r\n                        Create Announcement\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              </motion.div>\r\n\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.2 }}\r\n                className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] overflow-hidden\"\r\n              >\r\n                <div className=\"p-6\">\r\n                  <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4\">\r\n                    <h2 className=\"text-xl font-bold text-[#0077B6] flex items-center\">\r\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 text-[#0077B6]\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                        <path fillRule=\"evenodd\" d=\"M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z\" clipRule=\"evenodd\" />\r\n                      </svg>\r\n                      Recent Activity\r\n                    </h2>\r\n                    <button\r\n                      onClick={() => navigate('/superadmin/activity')}\r\n                      className=\"text-[#0077B6] hover:underline font-medium\"\r\n                    >\r\n                      View All\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"overflow-x-auto\">\r\n                    <table className=\"min-w-full divide-y divide-gray-200\">\r\n                      <thead className=\"bg-gray-50\">\r\n                        <tr>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Action</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">User</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Date</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                        {analytics.recentActivity.length === 0 ? (\r\n                          <tr>\r\n                            <td colSpan=\"3\" className=\"px-6 py-8 text-center\">\r\n                              <div className=\"flex flex-col items-center justify-center\">\r\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-12 w-12 text-gray-400 mb-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\r\n                                </svg>\r\n                                <h3 className=\"text-lg font-medium text-gray-900\">No recent activity</h3>\r\n                                <p className=\"mt-1 text-gray-500\">No actions have been logged recently.</p>\r\n                              </div>\r\n                            </td>\r\n                          </tr>\r\n                        ) : (\r\n                          analytics.recentActivity.map((activity, index) => (\r\n                            <motion.tr\r\n                              key={index}\r\n                              initial={{ opacity: 0 }}\r\n                              animate={{ opacity: 1 }}\r\n                              className=\"hover:bg-gray-50\"\r\n                            >\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{activity.action}</td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{activity.user}</td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                {new Date(activity.date).toLocaleDateString('en-US', {\r\n                                  weekday: 'short', month: 'short', day: 'numeric'\r\n                                })}\r\n                              </td>\r\n                            </motion.tr>\r\n                          ))\r\n                        )}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n\r\n      {showUniversityModal && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n          <motion.div\r\n            initial={{ scale: 0.9, opacity: 0 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            className=\"bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\"\r\n          >\r\n            <div className=\"p-6\">\r\n              <div className=\"flex justify-between items-center mb-6\">\r\n                <h2 className=\"text-2xl font-bold text-[#0077B6]\">{editUniversity ? 'Edit University' : 'Add University'}</h2>\r\n                <button onClick={() => { setShowUniversityModal(false); navigate('/superadmin/dashboard', { state: {} }); }} className=\"text-gray-400 hover:text-gray-500\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n              <form onSubmit={handleUniversitySubmit} className=\"space-y-6\">\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">University ID*</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={universityForm.universityId}\r\n                      onChange={(e) => setUniversityForm({ ...universityForm, universityId: e.target.value })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                      required\r\n                      disabled={editUniversity}\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Name (English)*</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={universityForm.name.en}\r\n                      onChange={(e) => setUniversityForm({ ...universityForm, name: { ...universityForm.name, en: e.target.value } })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Name (Arabic)*</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={universityForm.name.ar}\r\n                      onChange={(e) => setUniversityForm({ ...universityForm, name: { ...universityForm.name, ar: e.target.value } })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Phone*</label>\r\n                    <input\r\n                      type=\"tel\"\r\n                      value={universityForm.contactInfo.phone}\r\n                      onChange={(e) => setUniversityForm({ ...universityForm, contactInfo: { ...universityForm.contactInfo, phone: e.target.value } })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Email*</label>\r\n                    <input\r\n                      type=\"email\"\r\n                      value={universityForm.contactInfo.email}\r\n                      onChange={(e) => setUniversityForm({ ...universityForm, contactInfo: { ...universityForm.contactInfo, email: e.target.value } })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Website</label>\r\n                    <input\r\n                      type=\"url\"\r\n                      value={universityForm.contactInfo.website}\r\n                      onChange={(e) => setUniversityForm({ ...universityForm, contactInfo: { ...universityForm.contactInfo, website: e.target.value } })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Logo URL</label>\r\n                    <input\r\n                      type=\"url\"\r\n                      value={universityForm.logo}\r\n                      onChange={(e) => setUniversityForm({ ...universityForm, logo: e.target.value })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Image URL</label>\r\n                    <input\r\n                      type=\"url\"\r\n                      value={universityForm.image}\r\n                      onChange={(e) => setUniversityForm({ ...universityForm, image: e.target.value })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Map URL</label>\r\n                    <input\r\n                      type=\"url\"\r\n                      value={universityForm.mapUrl}\r\n                      onChange={(e) => setUniversityForm({ ...universityForm, mapUrl: e.target.value })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Slot Begin Date*</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      value={universityForm.slotBeginDate}\r\n                      onChange={(e) => setUniversityForm({ ...universityForm, slotBeginDate: e.target.value })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Slot End Date*</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      value={universityForm.slotEndDate}\r\n                      onChange={(e) => setUniversityForm({ ...universityForm, slotEndDate: e.target.value })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Description (English)</label>\r\n                  <textarea\r\n                    value={universityForm.description.en}\r\n                    onChange={(e) => setUniversityForm({ ...universityForm, description: { ...universityForm.description, en: e.target.value } })}\r\n                    rows=\"3\"\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Description (Arabic)</label>\r\n                  <textarea\r\n                    value={universityForm.description.ar}\r\n                    onChange={(e) => setUniversityForm({ ...universityForm, description: { ...universityForm.description, ar: e.target.value } })}\r\n                    rows=\"3\"\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Dentistry Info (English)</label>\r\n                  <textarea\r\n                    value={universityForm.dentistryInfo.en}\r\n                    onChange={(e) => setUniversityForm({ ...universityForm, dentistryInfo: { ...universityForm.dentistryInfo, en: e.target.value } })}\r\n                    rows=\"3\"\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Dentistry Info (Arabic)</label>\r\n                  <textarea\r\n                    value={universityForm.dentistryInfo.ar}\r\n                    onChange={(e) => setUniversityForm({ ...universityForm, dentistryInfo: { ...universityForm.dentistryInfo, ar: e.target.value } })}\r\n                    rows=\"3\"\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Facilities (English)</label>\r\n                  <textarea\r\n                    value={universityForm.facilities.en}\r\n                    onChange={(e) => setUniversityForm({ ...universityForm, facilities: { ...universityForm.facilities, en: e.target.value } })}\r\n                    rows=\"3\"\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Facilities (Arabic)</label>\r\n                  <textarea\r\n                    value={universityForm.facilities.ar}\r\n                    onChange={(e) => setUniversityForm({ ...universityForm, facilities: { ...universityForm.facilities, ar: e.target.value } })}\r\n                    rows=\"3\"\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Program (English)</label>\r\n                  <textarea\r\n                    value={universityForm.program.en}\r\n                    onChange={(e) => setUniversityForm({ ...universityForm, program: { ...universityForm.program, en: e.target.value } })}\r\n                    rows=\"3\"\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Program (Arabic)</label>\r\n                  <textarea\r\n                    value={universityForm.program.ar}\r\n                    onChange={(e) => setUniversityForm({ ...universityForm, program: { ...universityForm.program, ar: e.target.value } })}\r\n                    rows=\"3\"\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Dentistry Services</label>\r\n                  <div className=\"space-y-4\">\r\n                    {universityForm.dentistryServices.map((service, index) => (\r\n                      <div key={index} className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 border border-gray-200 rounded-lg\">\r\n                        <div>\r\n                          <label className=\"block text-xs font-medium text-gray-700 mb-1\">Service (English)</label>\r\n                          <input\r\n                            type=\"text\"\r\n                            value={service.en}\r\n                            onChange={(e) => {\r\n                              const newServices = [...universityForm.dentistryServices];\r\n                              newServices[index] = { ...newServices[index], en: e.target.value };\r\n                              setUniversityForm({ ...universityForm, dentistryServices: newServices });\r\n                            }}\r\n                            className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                          />\r\n                        </div>\r\n                        <div>\r\n                          <label className=\"block text-xs font-medium text-gray-700 mb-1\">Service (Arabic)</label>\r\n                          <input\r\n                            type=\"text\"\r\n                            value={service.ar}\r\n                            onChange={(e) => {\r\n                              const newServices = [...universityForm.dentistryServices];\r\n                              newServices[index] = { ...newServices[index], ar: e.target.value };\r\n                              setUniversityForm({ ...universityForm, dentistryServices: newServices });\r\n                            }}\r\n                            className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                          />\r\n                        </div>\r\n                        <div className=\"sm:col-span-2 flex justify-end\">\r\n                          <button\r\n                            type=\"button\"\r\n                            onClick={() => {\r\n                              const newServices = [...universityForm.dentistryServices];\r\n                              newServices.splice(index, 1);\r\n                              setUniversityForm({ ...universityForm, dentistryServices: newServices });\r\n                            }}\r\n                            className=\"text-red-500 hover:text-red-700\"\r\n                          >\r\n                            Remove\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => {\r\n                        setUniversityForm({\r\n                          ...universityForm,\r\n                          dentistryServices: [...universityForm.dentistryServices, { en: '', ar: '' }]\r\n                        });\r\n                      }}\r\n                      className=\"px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors\"\r\n                    >\r\n                      + Add Service\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Slot Settings</label>\r\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2 p-4 border border-gray-200 rounded-lg\">\r\n                    <div>\r\n                      <label className=\"block text-xs font-medium text-gray-700 mb-1\">Slot Duration (minutes)*</label>\r\n                      <input\r\n                        type=\"number\"\r\n                        value={universityForm.slotDuration}\r\n                        onChange={(e) => setUniversityForm({\r\n                          ...universityForm,\r\n                          slotDuration: parseInt(e.target.value)\r\n                        })}\r\n                        min=\"30\"\r\n                        step=\"30\"\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                        required\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-xs font-medium text-gray-700 mb-1\">Available Time Slots*</label>\r\n                      <div className=\"space-y-2\">\r\n                        {universityForm.availableSlots.map((slot, index) => (\r\n                          <div key={index} className=\"flex items-center space-x-2\">\r\n                            <input\r\n                              type=\"time\"\r\n                              value={slot}\r\n                              onChange={(e) => {\r\n                                const newSlots = [...universityForm.availableSlots];\r\n                                newSlots[index] = e.target.value;\r\n                                setUniversityForm({ ...universityForm, availableSlots: newSlots });\r\n                              }}\r\n                              className=\"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                              required\r\n                            />\r\n                            <button\r\n                              type=\"button\"\r\n                              onClick={() => {\r\n                                const newSlots = [...universityForm.availableSlots];\r\n                                newSlots.splice(index, 1);\r\n                                setUniversityForm({ ...universityForm, availableSlots: newSlots });\r\n                              }}\r\n                              className=\"text-red-500 hover:text-red-700\"\r\n                            >\r\n                              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                                <path fillRule=\"evenodd\" d=\"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                              </svg>\r\n                            </button>\r\n                          </div>\r\n                        ))}\r\n                        <button\r\n                          type=\"button\"\r\n                          onClick={() => {\r\n                            setUniversityForm({\r\n                              ...universityForm,\r\n                              availableSlots: [...universityForm.availableSlots, '09:00']\r\n                            });\r\n                          }}\r\n                          className=\"px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors w-full\"\r\n                        >\r\n                          + Add Time Slot\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Holidays</label>\r\n                  <div className=\"grid grid-cols-2 sm:grid-cols-4 gap-3 mt-2 p-4 border border-gray-200 rounded-lg\">\r\n                    {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (\r\n                      <div key={day} className=\"flex items-center\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          id={`holiday-${day}`}\r\n                          checked={universityForm.holidays.includes(day)}\r\n                          onChange={() => {\r\n                            const currentHolidays = [...universityForm.holidays];\r\n                            if (currentHolidays.includes(day)) {\r\n                              // Remove the day if it's already in the holidays array\r\n                              setUniversityForm({\r\n                                ...universityForm,\r\n                                holidays: currentHolidays.filter(holiday => holiday !== day)\r\n                              });\r\n                            } else {\r\n                              // Add the day if it's not in the holidays array\r\n                              setUniversityForm({\r\n                                ...universityForm,\r\n                                holidays: [...currentHolidays, day]\r\n                              });\r\n                            }\r\n                          }}\r\n                          className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\r\n                        />\r\n                        <label htmlFor={`holiday-${day}`} className=\"ml-2 block text-sm text-gray-700\">\r\n                          {day}\r\n                        </label>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                  <p className=\"mt-1 text-sm text-gray-500\">Select days when the university is closed.</p>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Address</label>\r\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2\">\r\n                    <div>\r\n                      <label className=\"block text-xs font-medium text-gray-700 mb-1\">Street (English)</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={universityForm.address.street.en}\r\n                        onChange={(e) => setUniversityForm({\r\n                          ...universityForm,\r\n                          address: {\r\n                            ...universityForm.address,\r\n                            street: {\r\n                              ...universityForm.address.street,\r\n                              en: e.target.value\r\n                            }\r\n                          }\r\n                        })}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-xs font-medium text-gray-700 mb-1\">Street (Arabic)</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={universityForm.address.street.ar}\r\n                        onChange={(e) => setUniversityForm({\r\n                          ...universityForm,\r\n                          address: {\r\n                            ...universityForm.address,\r\n                            street: {\r\n                              ...universityForm.address.street,\r\n                              ar: e.target.value\r\n                            }\r\n                          }\r\n                        })}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-xs font-medium text-gray-700 mb-1\">City (English)</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={universityForm.address.city.en}\r\n                        onChange={(e) => setUniversityForm({\r\n                          ...universityForm,\r\n                          address: {\r\n                            ...universityForm.address,\r\n                            city: {\r\n                              ...universityForm.address.city,\r\n                              en: e.target.value\r\n                            }\r\n                          }\r\n                        })}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-xs font-medium text-gray-700 mb-1\">City (Arabic)</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={universityForm.address.city.ar}\r\n                        onChange={(e) => setUniversityForm({\r\n                          ...universityForm,\r\n                          address: {\r\n                            ...universityForm.address,\r\n                            city: {\r\n                              ...universityForm.address.city,\r\n                              ar: e.target.value\r\n                            }\r\n                          }\r\n                        })}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-xs font-medium text-gray-700 mb-1\">Country (English)</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={universityForm.address.country.en}\r\n                        onChange={(e) => setUniversityForm({\r\n                          ...universityForm,\r\n                          address: {\r\n                            ...universityForm.address,\r\n                            country: {\r\n                              ...universityForm.address.country,\r\n                              en: e.target.value\r\n                            }\r\n                          }\r\n                        })}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-xs font-medium text-gray-700 mb-1\">Country (Arabic)</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={universityForm.address.country.ar}\r\n                        onChange={(e) => setUniversityForm({\r\n                          ...universityForm,\r\n                          address: {\r\n                            ...universityForm.address,\r\n                            country: {\r\n                              ...universityForm.address.country,\r\n                              ar: e.target.value\r\n                            }\r\n                          }\r\n                        })}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-xs font-medium text-gray-700 mb-1\">Postal Code</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={universityForm.address.postalCode}\r\n                        onChange={(e) => setUniversityForm({\r\n                          ...universityForm,\r\n                          address: {\r\n                            ...universityForm.address,\r\n                            postalCode: e.target.value\r\n                          }\r\n                        })}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex justify-end space-x-4 pt-4\">\r\n                  <motion.button\r\n                    type=\"button\"\r\n                    onClick={() => { setShowUniversityModal(false); navigate('/superadmin/dashboard', { state: {} }); }}\r\n                    className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\"\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                  >\r\n                    Cancel\r\n                  </motion.button>\r\n                  <motion.button\r\n                    type=\"submit\"\r\n                    className=\"px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 font-medium transition-colors shadow-md hover:shadow-lg\"\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                  >\r\n                    {editUniversity ? 'Update University' : 'Add University'}\r\n                  </motion.button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      )}\r\n\r\n      {showClinicModal && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n          <motion.div\r\n            initial={{ scale: 0.9, opacity: 0 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            className=\"bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\"\r\n          >\r\n            <div className=\"p-6\">\r\n              <div className=\"flex justify-between items-center mb-6\">\r\n                <h2 className=\"text-2xl font-bold text-blue-900\">{editClinic ? 'Edit Clinic' : 'Add Clinic'}</h2>\r\n                <button onClick={() => { setShowClinicModal(false); navigate('/superadmin/dashboard', { state: {} }); }} className=\"text-gray-400 hover:text-gray-500\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n              <form onSubmit={handleClinicSubmit} className=\"space-y-6\">\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Dentist ID*</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={clinicForm.dentistId}\r\n                      onChange={(e) => setClinicForm({ ...clinicForm, dentistId: e.target.value })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      required\r\n                      disabled={editClinic}\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Name (English)*</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={clinicForm.name.en}\r\n                      onChange={(e) => setClinicForm({ ...clinicForm, name: { ...clinicForm.name, en: e.target.value } })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Name (Arabic)*</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={clinicForm.name.ar}\r\n                      onChange={(e) => setClinicForm({ ...clinicForm, name: { ...clinicForm.name, ar: e.target.value } })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue- atre-blue-500\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Clinic Name (English)*</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={clinicForm.clinicName.en}\r\n                      onChange={(e) => setClinicForm({ ...clinicForm, clinicName: { ...clinicForm.clinicName, en: e.target.value } })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Clinic Name (Arabic)*</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={clinicForm.clinicName.ar}\r\n                      onChange={(e) => setClinicForm({ ...clinicForm, clinicName: { ...clinicForm.clinicName, ar: e.target.value } })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">City (English)*</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={clinicForm.address.city.en}\r\n                      onChange={(e) => setClinicForm({ ...clinicForm, address: { ...clinicForm.address, city: { ...clinicForm.address.city, en: e.target.value } } })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">City (Arabic)*</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={clinicForm.address.city.ar}\r\n                      onChange={(e) => setClinicForm({ ...clinicForm, address: { ...clinicForm.address, city: { ...clinicForm.address.city, ar: e.target.value } } })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Phone*</label>\r\n                    <input\r\n                      type=\"tel\"\r\n                      value={clinicForm.contactInfo.phone}\r\n                      onChange={(e) => setClinicForm({ ...clinicForm, contactInfo: { ...clinicForm.contactInfo, phone: e.target.value } })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Email*</label>\r\n                    <input\r\n                      type=\"email\"\r\n                      value={clinicForm.contactInfo.email}\r\n                      onChange={(e) => setClinicForm({ ...clinicForm, contactInfo: { ...clinicForm.contactInfo, email: e.target.value } })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Slot Begin Date*</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      value={clinicForm.slotBeginDate}\r\n                      onChange={(e) => setClinicForm({ ...clinicForm, slotBeginDate: e.target.value })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Slot End Date*</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      value={clinicForm.slotEndDate}\r\n                      onChange={(e) => setClinicForm({ ...clinicForm, slotEndDate: e.target.value })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">About (English) <span className=\"text-gray-400 text-xs\">(Optional)</span></label>\r\n                  <textarea\r\n                    value={clinicForm.about.en}\r\n                    onChange={(e) => setClinicForm({ ...clinicForm, about: { ...clinicForm.about, en: e.target.value } })}\r\n                    rows=\"3\"\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">About (Arabic) <span className=\"text-gray-400 text-xs\">(Optional)</span></label>\r\n                  <textarea\r\n                    value={clinicForm.about.ar}\r\n                    onChange={(e) => setClinicForm({ ...clinicForm, about: { ...clinicForm.about, ar: e.target.value } })}\r\n                    rows=\"3\"\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Services <span className=\"text-gray-400 text-xs\">(Optional)</span></label>\r\n                  <div className=\"space-y-4\">\r\n                    {clinicForm.services.map((service, index) => (\r\n                      <div key={index} className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 border border-gray-200 rounded-lg\">\r\n                        <div>\r\n                          <label className=\"block text-xs font-medium text-gray-700 mb-1\">Service (English)</label>\r\n                          <input\r\n                            type=\"text\"\r\n                            value={service.en}\r\n                            onChange={(e) => {\r\n                              const newServices = [...clinicForm.services];\r\n                              newServices[index] = { ...newServices[index], en: e.target.value };\r\n                              setClinicForm({ ...clinicForm, services: newServices });\r\n                            }}\r\n                            className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                          />\r\n                        </div>\r\n                        <div>\r\n                          <label className=\"block text-xs font-medium text-gray-700 mb-1\">Service (Arabic)</label>\r\n                          <input\r\n                            type=\"text\"\r\n                            value={service.ar}\r\n                            onChange={(e) => {\r\n                              const newServices = [...clinicForm.services];\r\n                              newServices[index] = { ...newServices[index], ar: e.target.value };\r\n                              setClinicForm({ ...clinicForm, services: newServices });\r\n                            }}\r\n                            className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                          />\r\n                        </div>\r\n                        <div className=\"sm:col-span-2 flex justify-end\">\r\n                          <button\r\n                            type=\"button\"\r\n                            onClick={() => {\r\n                              const newServices = [...clinicForm.services];\r\n                              newServices.splice(index, 1);\r\n                              setClinicForm({ ...clinicForm, services: newServices });\r\n                            }}\r\n                            className=\"text-red-500 hover:text-red-700\"\r\n                          >\r\n                            Remove\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => {\r\n                        setClinicForm({\r\n                          ...clinicForm,\r\n                          services: [...clinicForm.services, { en: '', ar: '' }]\r\n                        });\r\n                      }}\r\n                      className=\"px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors\"\r\n                    >\r\n                      + Add Service\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Slot Settings</label>\r\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2 p-4 border border-gray-200 rounded-lg\">\r\n                    <div>\r\n                      <label className=\"block text-xs font-medium text-gray-700 mb-1\">Slot Duration (minutes)*</label>\r\n                      <input\r\n                        type=\"number\"\r\n                        value={clinicForm.slotDuration}\r\n                        onChange={(e) => setClinicForm({\r\n                          ...clinicForm,\r\n                          slotDuration: parseInt(e.target.value)\r\n                        })}\r\n                        min=\"30\"\r\n                        step=\"30\"\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                        required\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-xs font-medium text-gray-700 mb-1\">Available Time Slots*</label>\r\n                      <div className=\"space-y-2\">\r\n                        {clinicForm.availableSlots.map((slot, index) => (\r\n                          <div key={index} className=\"flex items-center space-x-2\">\r\n                            <input\r\n                              type=\"time\"\r\n                              value={slot}\r\n                              onChange={(e) => {\r\n                                const newSlots = [...clinicForm.availableSlots];\r\n                                newSlots[index] = e.target.value;\r\n                                setClinicForm({ ...clinicForm, availableSlots: newSlots });\r\n                              }}\r\n                              className=\"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                              required\r\n                            />\r\n                            <button\r\n                              type=\"button\"\r\n                              onClick={() => {\r\n                                const newSlots = [...clinicForm.availableSlots];\r\n                                newSlots.splice(index, 1);\r\n                                setClinicForm({ ...clinicForm, availableSlots: newSlots });\r\n                              }}\r\n                              className=\"text-red-500 hover:text-red-700\"\r\n                            >\r\n                              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                                <path fillRule=\"evenodd\" d=\"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                              </svg>\r\n                            </button>\r\n                          </div>\r\n                        ))}\r\n                        <button\r\n                          type=\"button\"\r\n                          onClick={() => {\r\n                            setClinicForm({\r\n                              ...clinicForm,\r\n                              availableSlots: [...clinicForm.availableSlots, '09:00']\r\n                            });\r\n                          }}\r\n                          className=\"px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors w-full\"\r\n                        >\r\n                          + Add Time Slot\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Working Hours</label>\r\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2 p-4 border border-gray-200 rounded-lg\">\r\n                    {Object.entries(clinicForm.workingHours).map(([day, hours]) => (\r\n                      <div key={day} className=\"flex flex-col\">\r\n                        <label className=\"block text-xs font-medium text-gray-700 mb-1 capitalize\">{day}</label>\r\n                        <input\r\n                          type=\"text\"\r\n                          value={hours}\r\n                          onChange={(e) => setClinicForm({\r\n                            ...clinicForm,\r\n                            workingHours: {\r\n                              ...clinicForm.workingHours,\r\n                              [day]: e.target.value\r\n                            }\r\n                          })}\r\n                          placeholder=\"09:00-17:00 or Closed\"\r\n                          className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                        />\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Address <span className=\"text-gray-400 text-xs\">(Only City is required)</span></label>\r\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2\">\r\n                    <div>\r\n                      <label className=\"block text-xs font-medium text-gray-700 mb-1\">Street (English)</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={clinicForm.address.street.en}\r\n                        onChange={(e) => setClinicForm({\r\n                          ...clinicForm,\r\n                          address: {\r\n                            ...clinicForm.address,\r\n                            street: {\r\n                              ...clinicForm.address.street,\r\n                              en: e.target.value\r\n                            }\r\n                          }\r\n                        })}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-xs font-medium text-gray-700 mb-1\">Street (Arabic)</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={clinicForm.address.street.ar}\r\n                        onChange={(e) => setClinicForm({\r\n                          ...clinicForm,\r\n                          address: {\r\n                            ...clinicForm.address,\r\n                            street: {\r\n                              ...clinicForm.address.street,\r\n                              ar: e.target.value\r\n                            }\r\n                          }\r\n                        })}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-xs font-medium text-gray-700 mb-1\">Country (English)</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={clinicForm.address.country.en}\r\n                        onChange={(e) => setClinicForm({\r\n                          ...clinicForm,\r\n                          address: {\r\n                            ...clinicForm.address,\r\n                            country: {\r\n                              ...clinicForm.address.country,\r\n                              en: e.target.value\r\n                            }\r\n                          }\r\n                        })}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-xs font-medium text-gray-700 mb-1\">Country (Arabic)</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={clinicForm.address.country.ar}\r\n                        onChange={(e) => setClinicForm({\r\n                          ...clinicForm,\r\n                          address: {\r\n                            ...clinicForm.address,\r\n                            country: {\r\n                              ...clinicForm.address.country,\r\n                              ar: e.target.value\r\n                            }\r\n                          }\r\n                        })}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-xs font-medium text-gray-700 mb-1\">Postal Code</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={clinicForm.address.postalCode}\r\n                        onChange={(e) => setClinicForm({\r\n                          ...clinicForm,\r\n                          address: {\r\n                            ...clinicForm.address,\r\n                            postalCode: e.target.value\r\n                          }\r\n                        })}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex justify-end space-x-4 pt-4\">\r\n                  <motion.button\r\n                    type=\"button\"\r\n                    onClick={() => { setShowClinicModal(false); navigate('/superadmin/dashboard', { state: {} }); }}\r\n                    className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\"\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                  >\r\n                    Cancel\r\n                  </motion.button>\r\n                  <motion.button\r\n                    type=\"submit\"\r\n                    className=\"px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 font-medium transition-colors shadow-md hover:shadow-lg\"\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                  >\r\n                    {editClinic ? 'Update Clinic' : 'Add Clinic'}\r\n                  </motion.button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      )}\r\n\r\n      {showAccountModal && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n          <motion.div\r\n            initial={{ scale: 0.9, opacity: 0 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            className=\"bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\"\r\n          >\r\n            <div className=\"p-6\">\r\n              <div className=\"flex justify-between items-center mb-6\">\r\n                <h2 className=\"text-2xl font-bold text-blue-900\">Add Account</h2>\r\n                <button onClick={() => setShowAccountModal(false)} className=\"text-gray-400 hover:text-gray-500\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n              <form onSubmit={handleAccountSubmit} className=\"space-y-6\">\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Email*</label>\r\n                    <input\r\n                      type=\"email\"\r\n                      name=\"email\"\r\n                      value={accountForm.email}\r\n                      onChange={(e) => setAccountForm({ ...accountForm, email: e.target.value })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Password*</label>\r\n                    <input\r\n                      type=\"password\"\r\n                      name=\"password\"\r\n                      value={accountForm.password}\r\n                      onChange={(e) => setAccountForm({ ...accountForm, password: e.target.value })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      required\r\n                      minLength=\"6\"\r\n                    />\r\n                    <p className=\"text-xs text-gray-500 mt-1\">Password must be at least 6 characters long</p>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Name*</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      name=\"name\"\r\n                      value={accountForm.name}\r\n                      onChange={(e) => setAccountForm({ ...accountForm, name: e.target.value })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Role*</label>\r\n                    <select\r\n                      name=\"role\"\r\n                      value={accountForm.role}\r\n                      onChange={(e) => setAccountForm({ ...accountForm, role: e.target.value })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      required\r\n                    >\r\n                      <option value=\"student\">Student</option>\r\n                      <option value=\"supervisor\">Supervisor</option>\r\n                      <option value=\"admin\">Admin</option>\r\n                      <option value=\"assistant\">Assistant</option>\r\n                      <option value=\"dentist\">Dentist</option>\r\n                    </select>\r\n                  </div>\r\n                  {accountForm.role === 'student' && (\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">Student ID*</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        name=\"studentId\"\r\n                        value={accountForm.studentId}\r\n                        onChange={(e) => setAccountForm({ ...accountForm, studentId: e.target.value })}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                        required\r\n                      />\r\n                    </div>\r\n                  )}\r\n                  {['student', 'supervisor', 'admin', 'assistant'].includes(accountForm.role) && (\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">University*</label>\r\n                      <select\r\n                        name=\"universityId\"\r\n                        value={accountForm.universityId}\r\n                        onChange={(e) => setAccountForm({ ...accountForm, universityId: e.target.value })}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                        required\r\n                      >\r\n                        <option value=\"\">Select University</option>\r\n                        {universities.map((university) => (\r\n                          <option key={university.universityId} value={university.universityId}>\r\n                            {university.name.en}\r\n                          </option>\r\n                        ))}\r\n                      </select>\r\n                    </div>\r\n                  )}\r\n                  {accountForm.role === 'assistant' && (\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">Dentist*</label>\r\n                      <select\r\n                        name=\"dentistId\"\r\n                        value={accountForm.dentistId}\r\n                        onChange={(e) => setAccountForm({ ...accountForm, dentistId: e.target.value })}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                        required\r\n                      >\r\n                        <option value=\"\">Select Dentist</option>\r\n                        {clinics.map((clinic) => (\r\n                          <option key={clinic.dentistId} value={clinic.dentistId}>\r\n                            {clinic.name?.en || clinic.dentistId}\r\n                          </option>\r\n                        ))}\r\n                      </select>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n                <div className=\"flex justify-end space-x-4 pt-4\">\r\n                  <motion.button\r\n                    type=\"button\"\r\n                    onClick={() => setShowAccountModal(false)}\r\n                    className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\"\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                  >\r\n                    Cancel\r\n                  </motion.button>\r\n                  <motion.button\r\n                    type=\"submit\"\r\n                    className=\"px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 font-medium transition-colors shadow-md hover:shadow-lg\"\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                  >\r\n                    Add Account\r\n                  </motion.button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      )}\r\n\r\n      {showNewsModal && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n          <motion.div\r\n            initial={{ scale: 0.9, opacity: 0 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            className=\"bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-y-auto\"\r\n          >\r\n            <div className=\"p-6\">\r\n              <div className=\"flex justify-between items-center mb-6\">\r\n                <h2 className=\"text-2xl font-bold text-blue-900\">Send News</h2>\r\n                <button onClick={() => setShowNewsModal(false)} className=\"text-gray-400 hover:text-gray-500\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n              <form onSubmit={handleNewsSubmit} className=\"space-y-6\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Title*</label>\r\n                  <input\r\n                    type=\"text\"\r\n                    value={newsForm.title}\r\n                    onChange={(e) => setNewsForm({ ...newsForm, title: e.target.value })}\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Content*</label>\r\n                  <textarea\r\n                    value={newsForm.content}\r\n                    onChange={(e) => setNewsForm({ ...newsForm, content: e.target.value })}\r\n                    rows=\"4\"\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                    required\r\n                  />\r\n                </div>\r\n                <div className=\"flex items-center\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    id=\"isGlobal\"\r\n                    checked={newsForm.isGlobal}\r\n                    onChange={(e) => setNewsForm({ ...newsForm, isGlobal: e.target.checked })}\r\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\r\n                  />\r\n                  <label htmlFor=\"isGlobal\" className=\"ml-2 block text-sm text-gray-900\">\r\n                    Global Announcement (visible to all universities)\r\n                  </label>\r\n                </div>\r\n                {!newsForm.isGlobal && (\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">University ID*</label>\r\n                    <select\r\n                      value={newsForm.university}\r\n                      onChange={(e) => setNewsForm({ ...newsForm, university: e.target.value })}\r\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      required={!newsForm.isGlobal}\r\n                    >\r\n                      <option value=\"\">Select University</option>\r\n                      {universities.map((university) => (\r\n                        <option key={university.universityId} value={university.universityId}>\r\n                          {university.name.en}\r\n                        </option>\r\n                      ))}\r\n                    </select>\r\n                  </div>\r\n                )}\r\n                <div className=\"flex justify-end space-x-4 pt-4\">\r\n                  <motion.button\r\n                    type=\"button\"\r\n                    onClick={() => setShowNewsModal(false)}\r\n                    className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\"\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                  >\r\n                    Cancel\r\n                  </motion.button>\r\n                  <motion.button\r\n                    type=\"submit\"\r\n                    className=\"px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 font-medium transition-colors shadow-md hover:shadow-lg\"\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                  >\r\n                    Send News\r\n                  </motion.button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Success Modal */}\r\n      <SuccessModal\r\n        isOpen={showSuccessModal}\r\n        onClose={() => setShowSuccessModal(false)}\r\n        title={successTitle}\r\n        message={successMessage}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SuperAdminDashboard;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,YAAY,EAAEC,UAAU,EAAEC,WAAW,EAAEC,OAAO,QAAQ,gBAAgB;AAC/E,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACuB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,SAAS,CAAC;EAC3D,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC;IAAEyC,iBAAiB,EAAE,CAAC;IAAEC,YAAY,EAAE,CAAC;IAAEC,aAAa,EAAE,CAAC;IAAEC,cAAc,EAAE;EAAG,CAAC,CAAC;EAC3H,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+C,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMiD,QAAQ,GAAG/C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgD,IAAI;IAAEC;EAAM,CAAC,GAAGtC,OAAO,CAAC,CAAC;EACjC,MAAMuC,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkD,cAAc;IAAEC;EAAW,CAAC,GAAGF,QAAQ,CAACG,KAAK,IAAI,CAAC,CAAC;EAE3D,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,MAAM;IACzD;IACA,IAAIqD,cAAc,EAAE;MAClB;MACA,MAAMK,mBAAmB,GAAG;QAAE,GAAGL;MAAe,CAAC;;MAEjD;MACA,IAAIK,mBAAmB,CAACC,aAAa,EAAE;QACrCD,mBAAmB,CAACC,aAAa,GAAG,IAAIC,IAAI,CAACF,mBAAmB,CAACC,aAAa,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC7G;MAEA,IAAIJ,mBAAmB,CAACK,WAAW,EAAE;QACnCL,mBAAmB,CAACK,WAAW,GAAG,IAAIH,IAAI,CAACF,mBAAmB,CAACK,WAAW,CAAC,CAACF,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACzG;;MAEA;MACA,IAAIE,KAAK,CAACC,OAAO,CAACP,mBAAmB,CAACQ,SAAS,CAAC,IAAIR,mBAAmB,CAACQ,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;QAC5F;QACAT,mBAAmB,CAACU,cAAc,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACX,mBAAmB,CAACQ,SAAS,CAACI,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;MACzG,CAAC,MAAM;QACLd,mBAAmB,CAACU,cAAc,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;MACpE;MAEA,OAAOV,mBAAmB;IAC5B,CAAC,MAAM;MACL;MACA,OAAO;QACLe,YAAY,EAAE,EAAE;QAChBC,IAAI,EAAE;UAAEC,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QACxBC,WAAW,EAAE;UAAEF,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QAC/BE,aAAa,EAAE;UAAEH,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QACjCG,UAAU,EAAE;UAAEJ,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QAC9BI,OAAO,EAAE;UAAEL,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QAC3BK,iBAAiB,EAAE,CAAC;UAAEN,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC,CAAC;QACvCM,OAAO,EAAE;UACPC,MAAM,EAAE;YAAER,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG,CAAC;UAC1BQ,IAAI,EAAE;YAAET,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG,CAAC;UACxBS,OAAO,EAAE;YAAEV,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG,CAAC;UAC3BU,UAAU,EAAE;QACd,CAAC;QACDC,WAAW,EAAE;UACXC,KAAK,EAAE,EAAE;UACTC,KAAK,EAAE,EAAE;UACTC,OAAO,EAAE;QACX,CAAC;QACDC,KAAK,EAAE,EAAE;QACTC,IAAI,EAAE,EAAE;QACRC,MAAM,EAAE,EAAE;QACVlC,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrDC,WAAW,EAAE,IAAIH,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACkC,WAAW,CAAC,IAAIlC,IAAI,CAAC,CAAC,CAACmC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAClC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACvGkC,YAAY,EAAE,GAAG;QAAE;QACnB5B,cAAc,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;QAAE;QAC7C6B,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAE;MAClC,CAAC;IACH;EACF,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnG,QAAQ,CAAC,MAAM;IACjD;IACA,IAAIsD,UAAU,EAAE;MACd;MACA,MAAM8C,eAAe,GAAG;QAAE,GAAG9C;MAAW,CAAC;;MAEzC;MACA,IAAI8C,eAAe,CAACzC,aAAa,EAAE;QACjCyC,eAAe,CAACzC,aAAa,GAAG,IAAIC,IAAI,CAACwC,eAAe,CAACzC,aAAa,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACrG;MAEA,IAAIsC,eAAe,CAACrC,WAAW,EAAE;QAC/BqC,eAAe,CAACrC,WAAW,GAAG,IAAIH,IAAI,CAACwC,eAAe,CAACrC,WAAW,CAAC,CAACF,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACjG;;MAEA;MACA,IAAIE,KAAK,CAACC,OAAO,CAACmC,eAAe,CAAClC,SAAS,CAAC,IAAIkC,eAAe,CAAClC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;QACpF;QACAiC,eAAe,CAAChC,cAAc,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC+B,eAAe,CAAClC,SAAS,CAACI,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;MACjG,CAAC,MAAM;QACL4B,eAAe,CAAChC,cAAc,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;MACzE;;MAEA;MACA,IAAI,CAACgC,eAAe,CAACC,QAAQ,IAAI,CAACrC,KAAK,CAACC,OAAO,CAACmC,eAAe,CAACC,QAAQ,CAAC,EAAE;QACzED,eAAe,CAACC,QAAQ,GAAG,EAAE;MAC/B;MAEA,OAAOD,eAAe;IACxB,CAAC,MAAM;MACL;MACA,OAAO;QACLE,SAAS,EAAE,EAAE;QACb5B,IAAI,EAAE;UAAEC,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QACxB2B,UAAU,EAAE;UAAE5B,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QAC9B4B,KAAK,EAAE;UAAE7B,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QACzByB,QAAQ,EAAE,EAAE;QACZnB,OAAO,EAAE;UACPC,MAAM,EAAE;YAAER,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG,CAAC;UAC1BQ,IAAI,EAAE;YAAET,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG,CAAC;UACxBS,OAAO,EAAE;YAAEV,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG,CAAC;UAC3BU,UAAU,EAAE;QACd,CAAC;QACDC,WAAW,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAG,CAAC;QAClD/B,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrDC,WAAW,EAAE,IAAIH,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACkC,WAAW,CAAC,IAAIlC,IAAI,CAAC,CAAC,CAACmC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAClC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACvGkC,YAAY,EAAE,EAAE;QAAE;QAClB5B,cAAc,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;QAAE;QACtDqC,YAAY,EAAE;UACZC,MAAM,EAAE,aAAa;UACrBC,OAAO,EAAE,aAAa;UACtBC,SAAS,EAAE,aAAa;UACxBC,QAAQ,EAAE,aAAa;UACvBC,MAAM,EAAE,QAAQ;UAChBC,QAAQ,EAAE,aAAa;UACvBC,MAAM,EAAE;QACV,CAAC;QACDf,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAE;MAClC,CAAC;IACH;EACF,CAAC,CAAC;EAEF,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGlH,QAAQ,CAAC;IAC7CyF,KAAK,EAAE,EAAE;IACT0B,QAAQ,EAAE,EAAE;IACZzC,IAAI,EAAE,EAAE;IACR0C,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,EAAE;IACb5C,YAAY,EAAE,EAAE;IAChB6B,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGvH,QAAQ,CAAC;IACvCwH,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF1H,SAAS,CAAC,MAAM;IACd,MAAM2H,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI,CAAC1E,IAAI,IAAI,CAACC,KAAK,EAAE;QACnBH,QAAQ,CAAC,uCAAuC,CAAC;QACjDF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,IAAI;QACF,MAAM+E,MAAM,GAAG;UAAEC,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAU5E,KAAK;UAAG;QAAE,CAAC;QAChE;QACA,MAAM,CAAC6E,eAAe,EAAEC,UAAU,EAAEC,YAAY,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACpEhI,KAAK,CAACiI,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,mBAAmB,EAAEX,MAAM,CAAC,EACtEzH,KAAK,CAACiI,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,eAAe,EAAEX,MAAM,CAAC,EAClEzH,KAAK,CAACiI,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,4BAA4B,EAAEX,MAAM,CAAC,CAChF,CAAC;QAEFzF,eAAe,CAAC4F,eAAe,CAACS,IAAI,IAAI,EAAE,CAAC;QAC3CnG,UAAU,CAAC2F,UAAU,CAACQ,IAAI,IAAI,EAAE,CAAC;QACjCjG,YAAY,CAAC0F,YAAY,CAACO,IAAI,IAAI;UAAEhG,iBAAiB,EAAE,CAAC;UAAEC,YAAY,EAAE,CAAC;UAAEC,aAAa,EAAE,CAAC;UAAEC,cAAc,EAAE;QAAG,CAAC,CAAC;MACpH,CAAC,CAAC,OAAO8F,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA,EAAAC,cAAA;QACZC,OAAO,CAACjG,KAAK,CAAC,cAAc,EAAE,EAAA4F,aAAA,GAAAD,GAAG,CAACO,QAAQ,cAAAN,aAAA,uBAAZA,aAAA,CAAcF,IAAI,KAAIC,GAAG,CAACQ,OAAO,CAAC;QAChE,MAAMC,YAAY,GAAG,EAAAP,cAAA,GAAAF,GAAG,CAACO,QAAQ,cAAAL,cAAA,uBAAZA,cAAA,CAAcQ,MAAM,MAAK,GAAG,GAC7C,oCAAoC,GACpC,EAAAP,cAAA,GAAAH,GAAG,CAACO,QAAQ,cAAAJ,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcJ,IAAI,cAAAK,mBAAA,uBAAlBA,mBAAA,CAAoBI,OAAO,KAAI,qBAAqB;QACxDlG,QAAQ,CAACmG,YAAY,CAAC;QACtB,IAAI,EAAAJ,cAAA,GAAAL,GAAG,CAACO,QAAQ,cAAAF,cAAA,uBAAZA,cAAA,CAAcK,MAAM,MAAK,GAAG,EAAE;UAChCnG,QAAQ,CAAC,QAAQ,CAAC;QACpB;MACF,CAAC,SAAS;QACRH,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACD8E,SAAS,CAAC,CAAC;IAEX,IAAIvE,cAAc,EAAE/B,sBAAsB,CAAC,IAAI,CAAC;IAChD,IAAIgC,UAAU,EAAE9B,kBAAkB,CAAC,IAAI,CAAC;EAC1C,CAAC,EAAE,CAAC0B,IAAI,EAAEC,KAAK,EAAEF,QAAQ,EAAEI,cAAc,EAAEC,UAAU,CAAC,CAAC;EAEvD,MAAM+F,sBAAsB,GAAG,MAAOC,CAAC,IAAK;IAC1CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF;MACA,IAAI,CAAC/F,cAAc,CAACiB,YAAY,CAAC+E,IAAI,CAAC,CAAC,EAAE;QACvCxG,QAAQ,CAAC,2BAA2B,CAAC;QACrC;MACF;MAEA,IAAI,CAACQ,cAAc,CAACkB,IAAI,CAACC,EAAE,CAAC6E,IAAI,CAAC,CAAC,IAAI,CAAChG,cAAc,CAACkB,IAAI,CAACE,EAAE,CAAC4E,IAAI,CAAC,CAAC,EAAE;QACpExG,QAAQ,CAAC,wDAAwD,CAAC;QAClE;MACF;MAEA,IAAI,CAACQ,cAAc,CAAC+B,WAAW,CAACC,KAAK,CAACgE,IAAI,CAAC,CAAC,IAAI,CAAChG,cAAc,CAAC+B,WAAW,CAACE,KAAK,CAAC+D,IAAI,CAAC,CAAC,EAAE;QACxFxG,QAAQ,CAAC,mDAAmD,CAAC;QAC7D;MACF;;MAEA;MACA,MAAMyG,YAAY,GAAG;QAAE,GAAGjG;MAAe,CAAC;;MAE1C;MACA,CAAC,aAAa,EAAE,eAAe,EAAE,YAAY,EAAE,SAAS,CAAC,CAACkG,OAAO,CAACC,KAAK,IAAI;QACzE,IAAI,CAACF,YAAY,CAACE,KAAK,CAAC,CAAChF,EAAE,CAAC6E,IAAI,CAAC,CAAC,IAAI,CAACC,YAAY,CAACE,KAAK,CAAC,CAAC/E,EAAE,CAAC4E,IAAI,CAAC,CAAC,EAAE;UACpEC,YAAY,CAACE,KAAK,CAAC,GAAG;YACpBhF,EAAE,EAAE8E,YAAY,CAACE,KAAK,CAAC,CAAChF,EAAE,CAAC6E,IAAI,CAAC,CAAC,IAAI,cAAc;YACnD5E,EAAE,EAAE6E,YAAY,CAACE,KAAK,CAAC,CAAC/E,EAAE,CAAC4E,IAAI,CAAC,CAAC,IAAI;UACvC,CAAC;QACH;MACF,CAAC,CAAC;;MAEF;MACA,IAAIC,YAAY,CAACxE,iBAAiB,CAACd,MAAM,KAAK,CAAC,EAAE;QAC/CsF,YAAY,CAACxE,iBAAiB,GAAG,CAAC;UAAEN,EAAE,EAAE,mBAAmB;UAAEC,EAAE,EAAE;QAAmB,CAAC,CAAC;MACxF,CAAC,MAAM;QACL6E,YAAY,CAACxE,iBAAiB,GAAGwE,YAAY,CAACxE,iBAAiB,CAACX,GAAG,CAACsF,OAAO,KAAK;UAC9EjF,EAAE,EAAEiF,OAAO,CAACjF,EAAE,CAAC6E,IAAI,CAAC,CAAC,IAAI,mBAAmB;UAC5C5E,EAAE,EAAEgF,OAAO,CAAChF,EAAE,CAAC4E,IAAI,CAAC,CAAC,IAAI;QAC3B,CAAC,CAAC,CAAC;MACL;;MAEA;MACA,IAAI,CAACC,YAAY,CAACxD,QAAQ,IAAI,CAACjC,KAAK,CAACC,OAAO,CAACwF,YAAY,CAACxD,QAAQ,CAAC,IAAIwD,YAAY,CAACxD,QAAQ,CAAC9B,MAAM,KAAK,CAAC,EAAE;QACzG;QACAsF,YAAY,CAACxD,QAAQ,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC;MAC9C,CAAC,MAAM;QACL;QACAwD,YAAY,CAACxD,QAAQ,GAAGwD,YAAY,CAACxD,QAAQ,CAAC3B,GAAG,CAACuF,GAAG,IAAI;UACvD;UACA,OAAOA,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACjE,CAAC,CAAC;MACJ;MAEA,MAAMpC,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAU5E,KAAK;QAAG;MAAE,CAAC;MAChE,IAAIE,cAAc,EAAE;QAClB,MAAM4F,QAAQ,GAAG,MAAM7I,KAAK,CAAC8J,GAAG,CAAC,0CAA0CT,YAAY,CAAChF,YAAY,EAAE,EAAEgF,YAAY,EAAE5B,MAAM,CAAC;QAC7HzF,eAAe,CAACD,YAAY,CAACmC,GAAG,CAAC6F,CAAC,IAAIA,CAAC,CAAC1F,YAAY,KAAKgF,YAAY,CAAChF,YAAY,GAAGwE,QAAQ,CAACR,IAAI,GAAG0B,CAAC,CAAC,CAAC;QACxGjI,eAAe,CAAC,oBAAoB,CAAC;QACrCF,iBAAiB,CAAC,2CAA2C,CAAC;QAC9DF,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM;QACL,MAAMmH,QAAQ,GAAG,MAAM7I,KAAK,CAACgK,IAAI,CAAC,wCAAwC,EAAEX,YAAY,EAAE5B,MAAM,CAAC;QACjGzF,eAAe,CAAC,CAAC,GAAGD,YAAY,EAAE8G,QAAQ,CAACR,IAAI,CAAC,CAAC;QACjDvG,eAAe,CAAC,kBAAkB,CAAC;QACnCF,iBAAiB,CAAC,yCAAyC,CAAC;QAC5DF,mBAAmB,CAAC,IAAI,CAAC;MAC3B;MACAR,sBAAsB,CAAC,KAAK,CAAC;MAC7BmC,iBAAiB,CAAC;QAChBgB,YAAY,EAAE,EAAE;QAChBC,IAAI,EAAE;UAAEC,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QACxBC,WAAW,EAAE;UAAEF,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QAC/BE,aAAa,EAAE;UAAEH,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QACjCG,UAAU,EAAE;UAAEJ,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QAC9BI,OAAO,EAAE;UAAEL,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QAC3BK,iBAAiB,EAAE,CAAC;UAAEN,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC,CAAC;QACvCM,OAAO,EAAE;UACPC,MAAM,EAAE;YAAER,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG,CAAC;UAC1BQ,IAAI,EAAE;YAAET,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG,CAAC;UACxBS,OAAO,EAAE;YAAEV,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG,CAAC;UAC3BU,UAAU,EAAE;QACd,CAAC;QACDC,WAAW,EAAE;UACXC,KAAK,EAAE,EAAE;UACTC,KAAK,EAAE,EAAE;UACTC,OAAO,EAAE;QACX,CAAC;QACDC,KAAK,EAAE,EAAE;QACTC,IAAI,EAAE,EAAE;QACRC,MAAM,EAAE,EAAE;QACVlC,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrDC,WAAW,EAAE,IAAIH,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACkC,WAAW,CAAC,IAAIlC,IAAI,CAAC,CAAC,CAACmC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAClC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACvGkC,YAAY,EAAE,GAAG;QAAE;QACnB5B,cAAc,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;QAAE;QAC7C6B,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAE;MAClC,CAAC,CAAC;;MAEF;MACA,MAAMiC,YAAY,GAAG,MAAM9H,KAAK,CAACiI,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,4BAA4B,EAAEX,MAAM,CAAC;MAC1GrF,YAAY,CAAC0F,YAAY,CAACO,IAAI,IAAI;QAAEhG,iBAAiB,EAAE,CAAC;QAAEC,YAAY,EAAE,CAAC;QAAEC,aAAa,EAAE,CAAC;QAAEC,cAAc,EAAE;MAAG,CAAC,CAAC;MAElHK,QAAQ,CAAC,uBAAuB,EAAE;QAAEM,KAAK,EAAE,CAAC;MAAE,CAAC,CAAC;IAClD,CAAC,CAAC,OAAOmF,GAAG,EAAE;MAAA,IAAA2B,cAAA,EAAAC,mBAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZxB,OAAO,CAACjG,KAAK,CAAC,8BAA8B,EAAE2F,GAAG,CAAC;;MAElD;MACA,IAAIS,YAAY,GAAG,6BAA6B;MAEhD,KAAAkB,cAAA,GAAI3B,GAAG,CAACO,QAAQ,cAAAoB,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAc5B,IAAI,cAAA6B,mBAAA,eAAlBA,mBAAA,CAAoBvH,KAAK,EAAE;QAC7B;QACAoG,YAAY,IAAIT,GAAG,CAACO,QAAQ,CAACR,IAAI,CAAC1F,KAAK;MACzC,CAAC,MAAM,KAAAwH,cAAA,GAAI7B,GAAG,CAACO,QAAQ,cAAAsB,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAc9B,IAAI,cAAA+B,mBAAA,eAAlBA,mBAAA,CAAoBtB,OAAO,EAAE;QACtC;QACAC,YAAY,IAAIT,GAAG,CAACO,QAAQ,CAACR,IAAI,CAACS,OAAO;MAC3C,CAAC,MAAM,IAAIR,GAAG,CAACQ,OAAO,EAAE;QACtB;QACAC,YAAY,IAAIT,GAAG,CAACQ,OAAO;MAC7B;MAEAlG,QAAQ,CAACmG,YAAY,CAAC;IACxB;EACF,CAAC;EAED,MAAMsB,kBAAkB,GAAG,MAAOnB,CAAC,IAAK;IACtCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBvG,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF;MACA,IAAI,CAACM,UAAU,IAAI,CAAC4C,UAAU,CAACI,SAAS,CAACkD,IAAI,CAAC,CAAC,EAAE;QAC/C;QACA,MAAMkB,QAAQ,GAAG,GAAG,GAAGC,IAAI,CAACC,KAAK,CAAC,MAAM,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC;QAClE3E,UAAU,CAACI,SAAS,GAAGoE,QAAQ;QAC/B1B,OAAO,CAAC8B,GAAG,CAAC,2BAA2B,EAAEJ,QAAQ,CAAC;MACpD,CAAC,MAAM,IAAIpH,UAAU,IAAI,CAAC4C,UAAU,CAACI,SAAS,CAACkD,IAAI,CAAC,CAAC,EAAE;QACrDxG,QAAQ,CAAC,wBAAwB,CAAC;QAClC;MACF;MAEA,IAAI,CAACkD,UAAU,CAACxB,IAAI,CAACC,EAAE,CAAC6E,IAAI,CAAC,CAAC,IAAI,CAACtD,UAAU,CAACxB,IAAI,CAACE,EAAE,CAAC4E,IAAI,CAAC,CAAC,EAAE;QAC5DxG,QAAQ,CAAC,qDAAqD,CAAC;QAC/D;MACF;MAEA,IAAI,CAACkD,UAAU,CAACK,UAAU,CAAC5B,EAAE,CAAC6E,IAAI,CAAC,CAAC,IAAI,CAACtD,UAAU,CAACK,UAAU,CAAC3B,EAAE,CAAC4E,IAAI,CAAC,CAAC,EAAE;QACxExG,QAAQ,CAAC,oDAAoD,CAAC;QAC9D;MACF;MAEA,IAAI,CAACkD,UAAU,CAACX,WAAW,CAACC,KAAK,CAACgE,IAAI,CAAC,CAAC,EAAE;QACxCxG,QAAQ,CAAC,0BAA0B,CAAC;QACpC;MACF;MAEA,IAAI,CAACkD,UAAU,CAACX,WAAW,CAACE,KAAK,IAAI,CAACS,UAAU,CAACX,WAAW,CAACE,KAAK,CAAC+D,IAAI,CAAC,CAAC,EAAE;QACzExG,QAAQ,CAAC,mBAAmB,CAAC;QAC7B;MACF;;MAEA;MACA,MAAM+H,UAAU,GAAG,4BAA4B;MAC/C,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC9E,UAAU,CAACX,WAAW,CAACE,KAAK,CAAC+D,IAAI,CAAC,CAAC,CAAC,EAAE;QACzDxG,QAAQ,CAAC,oCAAoC,CAAC;QAC9C;MACF;;MAEA;MACA,IAAI,CAACkD,UAAU,CAAChB,OAAO,CAACE,IAAI,CAACT,EAAE,CAAC6E,IAAI,CAAC,CAAC,IAAI,CAACtD,UAAU,CAAChB,OAAO,CAACE,IAAI,CAACR,EAAE,CAAC4E,IAAI,CAAC,CAAC,EAAE;QAC5ExG,QAAQ,CAAC,6CAA6C,CAAC;QACvD;MACF;MAEA,IAAI,CAACkD,UAAU,CAACvC,aAAa,IAAI,CAACuC,UAAU,CAACnC,WAAW,EAAE;QACxDf,QAAQ,CAAC,uCAAuC,CAAC;QACjD;MACF;MAEA,IAAI,CAACkD,UAAU,CAACF,YAAY,IAAIE,UAAU,CAACF,YAAY,GAAG,EAAE,EAAE;QAC5DhD,QAAQ,CAAC,sDAAsD,CAAC;QAChE;MACF;MAEA,IAAI,CAACkD,UAAU,CAAC9B,cAAc,IAAI8B,UAAU,CAAC9B,cAAc,CAACD,MAAM,KAAK,CAAC,EAAE;QACxEnB,QAAQ,CAAC,8CAA8C,CAAC;QACxD;MACF;;MAEA;MACA,MAAMyG,YAAY,GAAG;QAAE,GAAGvD;MAAW,CAAC;;MAEtC;MACAuD,YAAY,CAAChE,KAAK,GAAGgE,YAAY,CAAClE,WAAW,CAACE,KAAK,CAAC+D,IAAI,CAAC,CAAC;;MAE1D;MACA,CAAC,OAAO,CAAC,CAACE,OAAO,CAACC,KAAK,IAAI;QACzB,IAAI,CAACF,YAAY,CAACE,KAAK,CAAC,CAAChF,EAAE,CAAC6E,IAAI,CAAC,CAAC,IAAI,CAACC,YAAY,CAACE,KAAK,CAAC,CAAC/E,EAAE,CAAC4E,IAAI,CAAC,CAAC,EAAE;UACpEC,YAAY,CAACE,KAAK,CAAC,GAAG;YACpBhF,EAAE,EAAE8E,YAAY,CAACE,KAAK,CAAC,CAAChF,EAAE,CAAC6E,IAAI,CAAC,CAAC,IAAI,cAAc;YACnD5E,EAAE,EAAE6E,YAAY,CAACE,KAAK,CAAC,CAAC/E,EAAE,CAAC4E,IAAI,CAAC,CAAC,IAAI;UACvC,CAAC;QACH;MACF,CAAC,CAAC;;MAEF;MACA,IAAI,CAACC,YAAY,CAACpD,QAAQ,IAAIoD,YAAY,CAACpD,QAAQ,CAAClC,MAAM,KAAK,CAAC,EAAE;QAChEsF,YAAY,CAACpD,QAAQ,GAAG,CAAC;UAAE1B,EAAE,EAAE,mBAAmB;UAAEC,EAAE,EAAE;QAAmB,CAAC,CAAC;MAC/E,CAAC,MAAM;QACL6E,YAAY,CAACpD,QAAQ,GAAGoD,YAAY,CAACpD,QAAQ,CAAC/B,GAAG,CAACsF,OAAO,KAAK;UAC5DjF,EAAE,EAAEiF,OAAO,CAACjF,EAAE,CAAC6E,IAAI,CAAC,CAAC,IAAI,mBAAmB;UAC5C5E,EAAE,EAAEgF,OAAO,CAAChF,EAAE,CAAC4E,IAAI,CAAC,CAAC,IAAI;QAC3B,CAAC,CAAC,CAAC;MACL;;MAEA;MACA,IAAI,CAACC,YAAY,CAACxD,QAAQ,IAAI,CAACjC,KAAK,CAACC,OAAO,CAACwF,YAAY,CAACxD,QAAQ,CAAC,IAAIwD,YAAY,CAACxD,QAAQ,CAAC9B,MAAM,KAAK,CAAC,EAAE;QACzG;QACAsF,YAAY,CAACxD,QAAQ,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC;MAC9C,CAAC,MAAM;QACL;QACAwD,YAAY,CAACxD,QAAQ,GAAGwD,YAAY,CAACxD,QAAQ,CAAC3B,GAAG,CAACuF,GAAG,IAAI;UACvD;UACA,OAAOA,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACjE,CAAC,CAAC;MACJ;;MAEA;MACA,MAAMgB,IAAI,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC;MAC3FA,IAAI,CAACvB,OAAO,CAACG,GAAG,IAAI;QAClB,IAAI,CAACJ,YAAY,CAAChD,YAAY,CAACoD,GAAG,CAAC,EAAE;UACnCJ,YAAY,CAAChD,YAAY,CAACoD,GAAG,CAAC,GAAG,QAAQ;QAC3C;MACF,CAAC,CAAC;MAEF,MAAMhC,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAU5E,KAAK;QAAG;MAAE,CAAC;MAEhE6F,OAAO,CAAC8B,GAAG,CAAC,yBAAyB,EAAErB,YAAY,CAAC;MAEpD,IAAInG,UAAU,EAAE;QACd,MAAM2F,QAAQ,GAAG,MAAM7I,KAAK,CAAC8J,GAAG,CAAC,GAAG5B,OAAO,CAACC,GAAG,CAACC,iBAAiB,iBAAiBiB,YAAY,CAACnD,SAAS,EAAE,EAAEmD,YAAY,EAAE5B,MAAM,CAAC;QACjIvF,UAAU,CAACD,OAAO,CAACiC,GAAG,CAAC4G,CAAC,IAAIA,CAAC,CAAC5E,SAAS,KAAKmD,YAAY,CAACnD,SAAS,GAAG2C,QAAQ,CAACR,IAAI,GAAGyC,CAAC,CAAC,CAAC;QACxFhJ,eAAe,CAAC,gBAAgB,CAAC;QACjCF,iBAAiB,CAAC,uCAAuC,CAAC;QAC1DF,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM;QACL,MAAMmH,QAAQ,GAAG,MAAM7I,KAAK,CAACgK,IAAI,CAAC,GAAG9B,OAAO,CAACC,GAAG,CAACC,iBAAiB,eAAe,EAAEiB,YAAY,EAAE5B,MAAM,CAAC;QACxGvF,UAAU,CAAC,CAAC,GAAGD,OAAO,EAAE4G,QAAQ,CAACR,IAAI,CAAC,CAAC;QACvCvG,eAAe,CAAC,cAAc,CAAC;QAC/BF,iBAAiB,CAAC,qCAAqC,CAAC;QACxDF,mBAAmB,CAAC,IAAI,CAAC;MAC3B;MACAN,kBAAkB,CAAC,KAAK,CAAC;MACzB2E,aAAa,CAAC;QACZG,SAAS,EAAE,EAAE;QACb5B,IAAI,EAAE;UAAEC,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QACxB2B,UAAU,EAAE;UAAE5B,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QAC9B4B,KAAK,EAAE;UAAE7B,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QACzByB,QAAQ,EAAE,EAAE;QACZnB,OAAO,EAAE;UACPC,MAAM,EAAE;YAAER,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG,CAAC;UAC1BQ,IAAI,EAAE;YAAET,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG,CAAC;UACxBS,OAAO,EAAE;YAAEV,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG,CAAC;UAC3BU,UAAU,EAAE;QACd,CAAC;QACDC,WAAW,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAG,CAAC;QAClD/B,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrDC,WAAW,EAAE,IAAIH,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACkC,WAAW,CAAC,IAAIlC,IAAI,CAAC,CAAC,CAACmC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAClC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACvGkC,YAAY,EAAE,EAAE;QAAE;QAClB5B,cAAc,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;QAAE;QACtDqC,YAAY,EAAE;UACZC,MAAM,EAAE,aAAa;UACrBC,OAAO,EAAE,aAAa;UACtBC,SAAS,EAAE,aAAa;UACxBC,QAAQ,EAAE,aAAa;UACvBC,MAAM,EAAE,QAAQ;UAChBC,QAAQ,EAAE,aAAa;UACvBC,MAAM,EAAE;QACV,CAAC;QACDf,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAE;MAClC,CAAC,CAAC;;MAEF;MACA,MAAMiC,YAAY,GAAG,MAAM9H,KAAK,CAACiI,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,4BAA4B,EAAEX,MAAM,CAAC;MAC1GrF,YAAY,CAAC0F,YAAY,CAACO,IAAI,IAAI;QAAEhG,iBAAiB,EAAE,CAAC;QAAEC,YAAY,EAAE,CAAC;QAAEC,aAAa,EAAE,CAAC;QAAEC,cAAc,EAAE;MAAG,CAAC,CAAC;MAElHK,QAAQ,CAAC,uBAAuB,EAAE;QAAEM,KAAK,EAAE,CAAC;MAAE,CAAC,CAAC;IAClD,CAAC,CAAC,OAAOmF,GAAG,EAAE;MAAA,IAAAyC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,oBAAA,EAAAC,eAAA,EAAAC,oBAAA;MACZzC,OAAO,CAACjG,KAAK,CAAC,0BAA0B,EAAE2F,GAAG,CAAC;;MAE9C;MACA,KAAAyC,cAAA,GAAIzC,GAAG,CAACO,QAAQ,cAAAkC,cAAA,eAAZA,cAAA,CAAc1C,IAAI,EAAE;QACtBO,OAAO,CAACjG,KAAK,CAAC,uBAAuB,EAAE2F,GAAG,CAACO,QAAQ,CAACR,IAAI,CAAC;MAC3D;MACA,KAAA2C,cAAA,GAAI1C,GAAG,CAACO,QAAQ,cAAAmC,cAAA,eAAZA,cAAA,CAAchC,MAAM,EAAE;QACxBJ,OAAO,CAACjG,KAAK,CAAC,yBAAyB,EAAE2F,GAAG,CAACO,QAAQ,CAACG,MAAM,CAAC;MAC/D;MACA,KAAAiC,cAAA,GAAI3C,GAAG,CAACO,QAAQ,cAAAoC,cAAA,eAAZA,cAAA,CAAcvD,OAAO,EAAE;QACzBkB,OAAO,CAACjG,KAAK,CAAC,0BAA0B,EAAE2F,GAAG,CAACO,QAAQ,CAACnB,OAAO,CAAC;MACjE;;MAEA;MACA,IAAIqB,YAAY,GAAG,yBAAyB;MAE5C,KAAAmC,eAAA,GAAI5C,GAAG,CAACO,QAAQ,cAAAqC,eAAA,gBAAAC,oBAAA,GAAZD,eAAA,CAAc7C,IAAI,cAAA8C,oBAAA,eAAlBA,oBAAA,CAAoBxI,KAAK,EAAE;QAC7B;QACAoG,YAAY,IAAIT,GAAG,CAACO,QAAQ,CAACR,IAAI,CAAC1F,KAAK;MACzC,CAAC,MAAM,KAAAyI,eAAA,GAAI9C,GAAG,CAACO,QAAQ,cAAAuC,eAAA,gBAAAC,oBAAA,GAAZD,eAAA,CAAc/C,IAAI,cAAAgD,oBAAA,eAAlBA,oBAAA,CAAoBvC,OAAO,EAAE;QACtC;QACAC,YAAY,IAAIT,GAAG,CAACO,QAAQ,CAACR,IAAI,CAACS,OAAO;MAC3C,CAAC,MAAM,IAAIR,GAAG,CAACQ,OAAO,EAAE;QACtB;QACAC,YAAY,IAAIT,GAAG,CAACQ,OAAO;MAC7B;MAEAlG,QAAQ,CAACmG,YAAY,CAAC;IACxB;EACF,CAAC;EAED,MAAMuC,mBAAmB,GAAG,MAAOpC,CAAC,IAAK;IACvCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF;MACA,IAAItC,WAAW,CAACG,IAAI,KAAK,SAAS,IAAI,CAACH,WAAW,CAACI,SAAS,EAAE;QAC5DrE,QAAQ,CAAC,6CAA6C,CAAC;QACvD;MACF;MAEA,IAAI,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC2I,QAAQ,CAAC1E,WAAW,CAACG,IAAI,CAAC,IAAI,CAACH,WAAW,CAACxC,YAAY,EAAE;QAC3GzB,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;MAEA,IAAIiE,WAAW,CAACG,IAAI,KAAK,WAAW,IAAI,CAACH,WAAW,CAACX,SAAS,EAAE;QAC9DtD,QAAQ,CAAC,+CAA+C,CAAC;QACzD;MACF;MAEA,MAAM6E,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAU5E,KAAK;QAAG;MAAE,CAAC;MAChE,MAAM/C,KAAK,CAACgK,IAAI,CAAC,oCAAoC,EAAEnD,WAAW,EAAEY,MAAM,CAAC;MAE3EnG,mBAAmB,CAAC,KAAK,CAAC;MAC1BwF,cAAc,CAAC;QACbzB,KAAK,EAAE,EAAE;QACT0B,QAAQ,EAAE,EAAE;QACZzC,IAAI,EAAE,EAAE;QACR0C,IAAI,EAAE,SAAS;QACfC,SAAS,EAAE,EAAE;QACb5C,YAAY,EAAE,EAAE;QAChB6B,SAAS,EAAE;MACb,CAAC,CAAC;;MAEF;MACApE,eAAe,CAAC,iBAAiB,CAAC;MAClCF,iBAAiB,CAAC,wCAAwC,CAAC;MAC3DF,mBAAmB,CAAC,IAAI,CAAC;;MAEzB;MACA,MAAMoG,YAAY,GAAG,MAAM9H,KAAK,CAACiI,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,4BAA4B,EAAEX,MAAM,CAAC;MAC1GrF,YAAY,CAAC0F,YAAY,CAACO,IAAI,IAAI;QAAEhG,iBAAiB,EAAE,CAAC;QAAEC,YAAY,EAAE,CAAC;QAAEC,aAAa,EAAE,CAAC;QAAEC,cAAc,EAAE;MAAG,CAAC,CAAC;IACpH,CAAC,CAAC,OAAO8F,GAAG,EAAE;MAAA,IAAAkD,eAAA,EAAAC,oBAAA;MACZ7C,OAAO,CAACjG,KAAK,CAAC,yBAAyB,EAAE2F,GAAG,CAAC;MAC7C1F,QAAQ,CAAC,EAAA4I,eAAA,GAAAlD,GAAG,CAACO,QAAQ,cAAA2C,eAAA,wBAAAC,oBAAA,GAAZD,eAAA,CAAcnD,IAAI,cAAAoD,oBAAA,uBAAlBA,oBAAA,CAAoB3C,OAAO,KAAI,uBAAuB,CAAC;IAClE;EACF,CAAC;EAED,MAAM4C,gBAAgB,GAAG,MAAOxC,CAAC,IAAK;IACpCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAM1B,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAU5E,KAAK;QAAG;MAAE,CAAC;MAChE,MAAM/C,KAAK,CAACgK,IAAI,CAAC,gCAAgC,EAAE9C,QAAQ,EAAEO,MAAM,CAAC;MACpEjG,gBAAgB,CAAC,KAAK,CAAC;MACvB2F,WAAW,CAAC;QAAEC,KAAK,EAAE,EAAE;QAAEC,OAAO,EAAE,EAAE;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAG,CAAC,CAAC;MACvE;MACAzF,eAAe,CAAC,WAAW,CAAC;MAC5BF,iBAAiB,CAAC,kCAAkC,CAAC;MACrDF,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAO4G,GAAG,EAAE;MAAA,IAAAqD,eAAA,EAAAC,oBAAA;MACZhD,OAAO,CAACjG,KAAK,CAAC,wBAAwB,EAAE2F,GAAG,CAAC;MAC5C1F,QAAQ,CAAC,EAAA+I,eAAA,GAAArD,GAAG,CAACO,QAAQ,cAAA8C,eAAA,wBAAAC,oBAAA,GAAZD,eAAA,CAActD,IAAI,cAAAuD,oBAAA,uBAAlBA,oBAAA,CAAoB9C,OAAO,KAAI,qBAAqB,CAAC;IAChE;EACF,CAAC;EAED,MAAM+C,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEE,UAAU,EAAE;QAAEC,eAAe,EAAE;MAAI;IAAE;EAC3D,CAAC;EAED,MAAMC,IAAI,GAAG;IACXL,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAG,CAAC;IAC7BJ,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAE;EAC3B,CAAC;EAED,IAAI3J,OAAO,EAAE;IACX,oBAAO7B,OAAA,CAACL,MAAM;MAAA8L,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnB;EAEA,oBACE5L,OAAA;IAAK6L,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvC9L,OAAA,CAACF,iBAAiB;MAACiM,MAAM,EAAE5L,WAAY;MAAC6L,SAAS,EAAE5L;IAAe;MAAAqL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAErE5L,OAAA;MAAK6L,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnD9L,OAAA,CAACN,MAAM;QAACuM,aAAa,EAAEA,CAAA,KAAM7L,cAAc,CAAC,CAACD,WAAW;MAAE;QAAAsL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE7D5L,OAAA;QAAM6L,SAAS,EAAC,0FAA0F;QAAAC,QAAA,eACxG9L,OAAA;UAAK6L,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAC/B/J,KAAK,iBACJ/B,OAAA,CAACX,MAAM,CAAC6M,GAAG;YACTC,OAAO,EAAE;cAAEhB,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCY,OAAO,EAAE;cAAEjB,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE;YAAE,CAAE;YAC9BK,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAE7E9L,OAAA;cAAK6L,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC9L,OAAA;gBAAKqM,KAAK,EAAC,4BAA4B;gBAACR,SAAS,EAAC,2BAA2B;gBAACS,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAT,QAAA,eACnH9L,OAAA;kBAAMwM,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,mNAAmN;kBAACC,QAAQ,EAAC;gBAAS;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjQ,CAAC,eACN5L,OAAA;gBAAG6L,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAE/J;cAAK;gBAAA0J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eAED5L,OAAA,CAACX,MAAM,CAAC6M,GAAG;YACTC,OAAO,EAAE;cAAEhB,OAAO,EAAE;YAAE,CAAE;YACxBiB,OAAO,EAAE;cAAEjB,OAAO,EAAE;YAAE,CAAE;YACxBE,UAAU,EAAE;cAAEsB,QAAQ,EAAE;YAAI,CAAE;YAAAb,QAAA,gBAE9B9L,OAAA;cAAK6L,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/F9L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAI6L,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAAC;gBAEnE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL5L,OAAA;kBAAG6L,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,GAAC,gBAAc,EAAC,CAAA5J,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,IAAI,KAAI,aAAa;gBAAA;kBAAA+H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC,eACN5L,OAAA;gBAAK6L,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,gBAC/D9L,OAAA,CAACX,MAAM,CAACuN,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAM1M,sBAAsB,CAAC,IAAI,CAAE;kBAC5CuL,SAAS,EAAC,oMAAoM;kBAAAC,QAAA,gBAE9M9L,OAAA,CAACV,YAAY;oBAACuM,SAAS,EAAC;kBAAc;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,kBAE3C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAChB5L,OAAA,CAACX,MAAM,CAACuN,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAMtM,mBAAmB,CAAC,IAAI,CAAE;kBACzCmL,SAAS,EAAC,oMAAoM;kBAAAC,QAAA,gBAE9M9L,OAAA,CAACT,UAAU;oBAACsM,SAAS,EAAC;kBAAc;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAEzC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5L,OAAA,CAACX,MAAM,CAAC6M,GAAG;cACTe,QAAQ,EAAEhC,SAAU;cACpBkB,OAAO,EAAC,QAAQ;cAChBe,WAAW,EAAC,MAAM;cAClBC,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBvB,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBAErE9L,OAAA,CAACX,MAAM,CAAC6M,GAAG;gBACTe,QAAQ,EAAE1B,IAAK;gBACfM,SAAS,EAAC,+JAA+J;gBACzKmB,OAAO,EAAEA,CAAA,KAAM/K,QAAQ,CAAC,0BAA0B,CAAE;gBAAA6J,QAAA,eAEpD9L,OAAA;kBAAK6L,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC9L,OAAA;oBAAK6L,SAAS,EAAC,yJAAyJ;oBAAAC,QAAA,eACtK9L,OAAA,CAACV,YAAY;sBAACuM,SAAS,EAAC;oBAAwB;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,eACN5L,OAAA;oBAAA8L,QAAA,gBACE9L,OAAA;sBAAG6L,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACvE5L,OAAA;sBAAG6L,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEvK,SAAS,CAACE;oBAAiB;sBAAAgK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAEb5L,OAAA,CAACX,MAAM,CAAC6M,GAAG;gBACTe,QAAQ,EAAE1B,IAAK;gBACfM,SAAS,EAAC,+JAA+J;gBACzKmB,OAAO,EAAEA,CAAA,KAAM/K,QAAQ,CAAC,sBAAsB,CAAE;gBAAA6J,QAAA,eAEhD9L,OAAA;kBAAK6L,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC9L,OAAA;oBAAK6L,SAAS,EAAC,yJAAyJ;oBAAAC,QAAA,eACtK9L,OAAA,CAACP,OAAO;sBAACoM,SAAS,EAAC;oBAAwB;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,eACN5L,OAAA;oBAAA8L,QAAA,gBACE9L,OAAA;sBAAG6L,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAc;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACnE5L,OAAA;sBAAG6L,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEvK,SAAS,CAACI;oBAAa;sBAAA8J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAEb5L,OAAA,CAACX,MAAM,CAAC6M,GAAG;gBACTe,QAAQ,EAAE1B,IAAK;gBACfM,SAAS,EAAC,+JAA+J;gBACzKmB,OAAO,EAAEA,CAAA,KAAM/K,QAAQ,CAAC,kBAAkB,CAAE;gBAAA6J,QAAA,eAE5C9L,OAAA;kBAAK6L,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC9L,OAAA;oBAAK6L,SAAS,EAAC,yJAAyJ;oBAAAC,QAAA,eACtK9L,OAAA,CAACR,WAAW;sBAACqM,SAAS,EAAC;oBAAwB;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACN5L,OAAA;oBAAA8L,QAAA,gBACE9L,OAAA;sBAAG6L,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAI;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACzD5L,OAAA;sBACEgN,OAAO,EAAG1E,CAAC,IAAK;wBACdA,CAAC,CAAC+E,eAAe,CAAC,CAAC,CAAC,CAAC;wBACrBzM,gBAAgB,CAAC,IAAI,CAAC;sBACxB,CAAE;sBACFiL,SAAS,EAAC,oDAAoD;sBAAAC,QAAA,EAC/D;oBAED;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEb5L,OAAA,CAACX,MAAM,CAAC6M,GAAG;cACTC,OAAO,EAAE;gBAAEhB,OAAO,EAAE,CAAC;gBAAEK,CAAC,EAAE;cAAG,CAAE;cAC/BY,OAAO,EAAE;gBAAEjB,OAAO,EAAE,CAAC;gBAAEK,CAAC,EAAE;cAAE,CAAE;cAC9BH,UAAU,EAAE;gBAAEiC,KAAK,EAAE;cAAI,CAAE;cAC3BzB,SAAS,EAAC,+HAA+H;cAAAC,QAAA,eAEzI9L,OAAA;gBAAK6L,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClB9L,OAAA;kBAAK6L,SAAS,EAAC,kFAAkF;kBAAAC,QAAA,gBAC/F9L,OAAA;oBAAI6L,SAAS,EAAC,oDAAoD;oBAAAC,QAAA,gBAChE9L,OAAA;sBAAKqM,KAAK,EAAC,4BAA4B;sBAACR,SAAS,EAAC,6BAA6B;sBAACS,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,cAAc;sBAAAT,QAAA,eACrH9L,OAAA;wBAAMwM,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,4KAA4K;wBAACC,QAAQ,EAAC;sBAAS;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1N,CAAC,mBAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL5L,OAAA;oBACEgN,OAAO,EAAEA,CAAA,KAAM/K,QAAQ,CAAC,sBAAsB,CAAE;oBAChD4J,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,EACvD;kBAED;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAEN5L,OAAA;kBAAK6L,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC9B9L,OAAA;oBAAO6L,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,gBACpD9L,OAAA;sBAAO6L,SAAS,EAAC,YAAY;sBAAAC,QAAA,eAC3B9L,OAAA;wBAAA8L,QAAA,gBACE9L,OAAA;0BAAI6L,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAM;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC1G5L,OAAA;0BAAI6L,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAI;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACxG5L,OAAA;0BAAI6L,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAI;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACR5L,OAAA;sBAAO6L,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EACjDvK,SAAS,CAACK,cAAc,CAACuB,MAAM,KAAK,CAAC,gBACpCnD,OAAA;wBAAA8L,QAAA,eACE9L,OAAA;0BAAIuN,OAAO,EAAC,GAAG;0BAAC1B,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,eAC/C9L,OAAA;4BAAK6L,SAAS,EAAC,2CAA2C;4BAAAC,QAAA,gBACxD9L,OAAA;8BAAKqM,KAAK,EAAC,4BAA4B;8BAACR,SAAS,EAAC,8BAA8B;8BAACU,IAAI,EAAC,MAAM;8BAACD,OAAO,EAAC,WAAW;8BAACkB,MAAM,EAAC,cAAc;8BAAA1B,QAAA,eACpI9L,OAAA;gCAAMyN,aAAa,EAAC,OAAO;gCAACC,cAAc,EAAC,OAAO;gCAACC,WAAW,EAAE,CAAE;gCAAClB,CAAC,EAAC;8BAAiI;gCAAAhB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACtM,CAAC,eACN5L,OAAA;8BAAI6L,SAAS,EAAC,mCAAmC;8BAAAC,QAAA,EAAC;4BAAkB;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACzE5L,OAAA;8BAAG6L,SAAS,EAAC,oBAAoB;8BAAAC,QAAA,EAAC;4BAAqC;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,GAELrK,SAAS,CAACK,cAAc,CAAC0B,GAAG,CAAC,CAACsK,QAAQ,EAAEC,KAAK,kBAC3C7N,OAAA,CAACX,MAAM,CAACyO,EAAE;wBAER3B,OAAO,EAAE;0BAAEhB,OAAO,EAAE;wBAAE,CAAE;wBACxBiB,OAAO,EAAE;0BAAEjB,OAAO,EAAE;wBAAE,CAAE;wBACxBU,SAAS,EAAC,kBAAkB;wBAAAC,QAAA,gBAE5B9L,OAAA;0BAAI6L,SAAS,EAAC,mDAAmD;0BAAAC,QAAA,EAAE8B,QAAQ,CAACG;wBAAM;0BAAAtC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACxF5L,OAAA;0BAAI6L,SAAS,EAAC,mDAAmD;0BAAAC,QAAA,EAAE8B,QAAQ,CAAC1L;wBAAI;0BAAAuJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACtF5L,OAAA;0BAAI6L,SAAS,EAAC,mDAAmD;0BAAAC,QAAA,EAC9D,IAAIlJ,IAAI,CAACgL,QAAQ,CAACI,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;4BACnDC,OAAO,EAAE,OAAO;4BAAEC,KAAK,EAAE,OAAO;4BAAEtF,GAAG,EAAE;0BACzC,CAAC;wBAAC;0BAAA4C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC;sBAAA,GAXAiC,KAAK;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAYD,CACZ;oBACF;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAELvL,mBAAmB,iBAClBL,OAAA;MAAK6L,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F9L,OAAA,CAACX,MAAM,CAAC6M,GAAG;QACTC,OAAO,EAAE;UAAEW,KAAK,EAAE,GAAG;UAAE3B,OAAO,EAAE;QAAE,CAAE;QACpCiB,OAAO,EAAE;UAAEU,KAAK,EAAE,CAAC;UAAE3B,OAAO,EAAE;QAAE,CAAE;QAClCU,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAEzF9L,OAAA;UAAK6L,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB9L,OAAA;YAAK6L,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD9L,OAAA;cAAI6L,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEzJ,cAAc,GAAG,iBAAiB,GAAG;YAAgB;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9G5L,OAAA;cAAQgN,OAAO,EAAEA,CAAA,KAAM;gBAAE1M,sBAAsB,CAAC,KAAK,CAAC;gBAAE2B,QAAQ,CAAC,uBAAuB,EAAE;kBAAEM,KAAK,EAAE,CAAC;gBAAE,CAAC,CAAC;cAAE,CAAE;cAACsJ,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eACxJ9L,OAAA;gBAAKqM,KAAK,EAAC,4BAA4B;gBAACR,SAAS,EAAC,SAAS;gBAACU,IAAI,EAAC,MAAM;gBAACD,OAAO,EAAC,WAAW;gBAACkB,MAAM,EAAC,cAAc;gBAAA1B,QAAA,eAC/G9L,OAAA;kBAAMyN,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAAClB,CAAC,EAAC;gBAAsB;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN5L,OAAA;YAAMoO,QAAQ,EAAE/F,sBAAuB;YAACwD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAC3D9L,OAAA;cAAK6L,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD9L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAc;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtF5L,OAAA;kBACEqO,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAE9L,cAAc,CAACiB,YAAa;kBACnC8K,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;oBAAE,GAAGD,cAAc;oBAAEiB,YAAY,EAAE6E,CAAC,CAACkG,MAAM,CAACF;kBAAM,CAAC,CAAE;kBACxFzC,SAAS,EAAC,6GAA6G;kBACvH4C,QAAQ;kBACRC,QAAQ,EAAErM;gBAAe;kBAAAoJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAe;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvF5L,OAAA;kBACEqO,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAE9L,cAAc,CAACkB,IAAI,CAACC,EAAG;kBAC9B4K,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;oBAAE,GAAGD,cAAc;oBAAEkB,IAAI,EAAE;sBAAE,GAAGlB,cAAc,CAACkB,IAAI;sBAAEC,EAAE,EAAE2E,CAAC,CAACkG,MAAM,CAACF;oBAAM;kBAAE,CAAC,CAAE;kBAChHzC,SAAS,EAAC,6GAA6G;kBACvH4C,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAc;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtF5L,OAAA;kBACEqO,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAE9L,cAAc,CAACkB,IAAI,CAACE,EAAG;kBAC9B2K,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;oBAAE,GAAGD,cAAc;oBAAEkB,IAAI,EAAE;sBAAE,GAAGlB,cAAc,CAACkB,IAAI;sBAAEE,EAAE,EAAE0E,CAAC,CAACkG,MAAM,CAACF;oBAAM;kBAAE,CAAC,CAAE;kBAChHzC,SAAS,EAAC,6GAA6G;kBACvH4C,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAM;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9E5L,OAAA;kBACEqO,IAAI,EAAC,KAAK;kBACVC,KAAK,EAAE9L,cAAc,CAAC+B,WAAW,CAACC,KAAM;kBACxC+J,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;oBAAE,GAAGD,cAAc;oBAAE+B,WAAW,EAAE;sBAAE,GAAG/B,cAAc,CAAC+B,WAAW;sBAAEC,KAAK,EAAE8D,CAAC,CAACkG,MAAM,CAACF;oBAAM;kBAAE,CAAC,CAAE;kBACjIzC,SAAS,EAAC,6GAA6G;kBACvH4C,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAM;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9E5L,OAAA;kBACEqO,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAE9L,cAAc,CAAC+B,WAAW,CAACE,KAAM;kBACxC8J,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;oBAAE,GAAGD,cAAc;oBAAE+B,WAAW,EAAE;sBAAE,GAAG/B,cAAc,CAAC+B,WAAW;sBAAEE,KAAK,EAAE6D,CAAC,CAACkG,MAAM,CAACF;oBAAM;kBAAE,CAAC,CAAE;kBACjIzC,SAAS,EAAC,6GAA6G;kBACvH4C,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAO;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/E5L,OAAA;kBACEqO,IAAI,EAAC,KAAK;kBACVC,KAAK,EAAE9L,cAAc,CAAC+B,WAAW,CAACG,OAAQ;kBAC1C6J,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;oBAAE,GAAGD,cAAc;oBAAE+B,WAAW,EAAE;sBAAE,GAAG/B,cAAc,CAAC+B,WAAW;sBAAEG,OAAO,EAAE4D,CAAC,CAACkG,MAAM,CAACF;oBAAM;kBAAE,CAAC,CAAE;kBACnIzC,SAAS,EAAC;gBAA6G;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChF5L,OAAA;kBACEqO,IAAI,EAAC,KAAK;kBACVC,KAAK,EAAE9L,cAAc,CAACoC,IAAK;kBAC3B2J,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;oBAAE,GAAGD,cAAc;oBAAEoC,IAAI,EAAE0D,CAAC,CAACkG,MAAM,CAACF;kBAAM,CAAC,CAAE;kBAChFzC,SAAS,EAAC;gBAA6G;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAS;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjF5L,OAAA;kBACEqO,IAAI,EAAC,KAAK;kBACVC,KAAK,EAAE9L,cAAc,CAACmC,KAAM;kBAC5B4J,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;oBAAE,GAAGD,cAAc;oBAAEmC,KAAK,EAAE2D,CAAC,CAACkG,MAAM,CAACF;kBAAM,CAAC,CAAE;kBACjFzC,SAAS,EAAC;gBAA6G;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAO;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/E5L,OAAA;kBACEqO,IAAI,EAAC,KAAK;kBACVC,KAAK,EAAE9L,cAAc,CAACqC,MAAO;kBAC7B0J,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;oBAAE,GAAGD,cAAc;oBAAEqC,MAAM,EAAEyD,CAAC,CAACkG,MAAM,CAACF;kBAAM,CAAC,CAAE;kBAClFzC,SAAS,EAAC;gBAA6G;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxF5L,OAAA;kBACEqO,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAE9L,cAAc,CAACG,aAAc;kBACpC4L,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;oBAAE,GAAGD,cAAc;oBAAEG,aAAa,EAAE2F,CAAC,CAACkG,MAAM,CAACF;kBAAM,CAAC,CAAE;kBACzFzC,SAAS,EAAC,6GAA6G;kBACvH4C,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAc;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtF5L,OAAA;kBACEqO,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAE9L,cAAc,CAACO,WAAY;kBAClCwL,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;oBAAE,GAAGD,cAAc;oBAAEO,WAAW,EAAEuF,CAAC,CAACkG,MAAM,CAACF;kBAAM,CAAC,CAAE;kBACvFzC,SAAS,EAAC,6GAA6G;kBACvH4C,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAqB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7F5L,OAAA;gBACEsO,KAAK,EAAE9L,cAAc,CAACqB,WAAW,CAACF,EAAG;gBACrC4K,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;kBAAE,GAAGD,cAAc;kBAAEqB,WAAW,EAAE;oBAAE,GAAGrB,cAAc,CAACqB,WAAW;oBAAEF,EAAE,EAAE2E,CAAC,CAACkG,MAAM,CAACF;kBAAM;gBAAE,CAAC,CAAE;gBAC9HK,IAAI,EAAC,GAAG;gBACR9C,SAAS,EAAC;cAA2G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAoB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5F5L,OAAA;gBACEsO,KAAK,EAAE9L,cAAc,CAACqB,WAAW,CAACD,EAAG;gBACrC2K,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;kBAAE,GAAGD,cAAc;kBAAEqB,WAAW,EAAE;oBAAE,GAAGrB,cAAc,CAACqB,WAAW;oBAAED,EAAE,EAAE0E,CAAC,CAACkG,MAAM,CAACF;kBAAM;gBAAE,CAAC,CAAE;gBAC9HK,IAAI,EAAC,GAAG;gBACR9C,SAAS,EAAC;cAA2G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAwB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChG5L,OAAA;gBACEsO,KAAK,EAAE9L,cAAc,CAACsB,aAAa,CAACH,EAAG;gBACvC4K,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;kBAAE,GAAGD,cAAc;kBAAEsB,aAAa,EAAE;oBAAE,GAAGtB,cAAc,CAACsB,aAAa;oBAAEH,EAAE,EAAE2E,CAAC,CAACkG,MAAM,CAACF;kBAAM;gBAAE,CAAC,CAAE;gBAClIK,IAAI,EAAC,GAAG;gBACR9C,SAAS,EAAC;cAA2G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAuB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/F5L,OAAA;gBACEsO,KAAK,EAAE9L,cAAc,CAACsB,aAAa,CAACF,EAAG;gBACvC2K,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;kBAAE,GAAGD,cAAc;kBAAEsB,aAAa,EAAE;oBAAE,GAAGtB,cAAc,CAACsB,aAAa;oBAAEF,EAAE,EAAE0E,CAAC,CAACkG,MAAM,CAACF;kBAAM;gBAAE,CAAC,CAAE;gBAClIK,IAAI,EAAC,GAAG;gBACR9C,SAAS,EAAC;cAA2G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAoB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5F5L,OAAA;gBACEsO,KAAK,EAAE9L,cAAc,CAACuB,UAAU,CAACJ,EAAG;gBACpC4K,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;kBAAE,GAAGD,cAAc;kBAAEuB,UAAU,EAAE;oBAAE,GAAGvB,cAAc,CAACuB,UAAU;oBAAEJ,EAAE,EAAE2E,CAAC,CAACkG,MAAM,CAACF;kBAAM;gBAAE,CAAC,CAAE;gBAC5HK,IAAI,EAAC,GAAG;gBACR9C,SAAS,EAAC;cAA2G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAmB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3F5L,OAAA;gBACEsO,KAAK,EAAE9L,cAAc,CAACuB,UAAU,CAACH,EAAG;gBACpC2K,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;kBAAE,GAAGD,cAAc;kBAAEuB,UAAU,EAAE;oBAAE,GAAGvB,cAAc,CAACuB,UAAU;oBAAEH,EAAE,EAAE0E,CAAC,CAACkG,MAAM,CAACF;kBAAM;gBAAE,CAAC,CAAE;gBAC5HK,IAAI,EAAC,GAAG;gBACR9C,SAAS,EAAC;cAA2G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAiB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzF5L,OAAA;gBACEsO,KAAK,EAAE9L,cAAc,CAACwB,OAAO,CAACL,EAAG;gBACjC4K,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;kBAAE,GAAGD,cAAc;kBAAEwB,OAAO,EAAE;oBAAE,GAAGxB,cAAc,CAACwB,OAAO;oBAAEL,EAAE,EAAE2E,CAAC,CAACkG,MAAM,CAACF;kBAAM;gBAAE,CAAC,CAAE;gBACtHK,IAAI,EAAC,GAAG;gBACR9C,SAAS,EAAC;cAA2G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAgB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxF5L,OAAA;gBACEsO,KAAK,EAAE9L,cAAc,CAACwB,OAAO,CAACJ,EAAG;gBACjC2K,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;kBAAE,GAAGD,cAAc;kBAAEwB,OAAO,EAAE;oBAAE,GAAGxB,cAAc,CAACwB,OAAO;oBAAEJ,EAAE,EAAE0E,CAAC,CAACkG,MAAM,CAACF;kBAAM;gBAAE,CAAC,CAAE;gBACtHK,IAAI,EAAC,GAAG;gBACR9C,SAAS,EAAC;cAA2G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAkB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1F5L,OAAA;gBAAK6L,SAAS,EAAC,WAAW;gBAAAC,QAAA,GACvBtJ,cAAc,CAACyB,iBAAiB,CAACX,GAAG,CAAC,CAACsF,OAAO,EAAEiF,KAAK,kBACnD7N,OAAA;kBAAiB6L,SAAS,EAAC,6EAA6E;kBAAAC,QAAA,gBACtG9L,OAAA;oBAAA8L,QAAA,gBACE9L,OAAA;sBAAO6L,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACzF5L,OAAA;sBACEqO,IAAI,EAAC,MAAM;sBACXC,KAAK,EAAE1F,OAAO,CAACjF,EAAG;sBAClB4K,QAAQ,EAAGjG,CAAC,IAAK;wBACf,MAAMsG,WAAW,GAAG,CAAC,GAAGpM,cAAc,CAACyB,iBAAiB,CAAC;wBACzD2K,WAAW,CAACf,KAAK,CAAC,GAAG;0BAAE,GAAGe,WAAW,CAACf,KAAK,CAAC;0BAAElK,EAAE,EAAE2E,CAAC,CAACkG,MAAM,CAACF;wBAAM,CAAC;wBAClE7L,iBAAiB,CAAC;0BAAE,GAAGD,cAAc;0BAAEyB,iBAAiB,EAAE2K;wBAAY,CAAC,CAAC;sBAC1E,CAAE;sBACF/C,SAAS,EAAC;oBAA2G;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACN5L,OAAA;oBAAA8L,QAAA,gBACE9L,OAAA;sBAAO6L,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACxF5L,OAAA;sBACEqO,IAAI,EAAC,MAAM;sBACXC,KAAK,EAAE1F,OAAO,CAAChF,EAAG;sBAClB2K,QAAQ,EAAGjG,CAAC,IAAK;wBACf,MAAMsG,WAAW,GAAG,CAAC,GAAGpM,cAAc,CAACyB,iBAAiB,CAAC;wBACzD2K,WAAW,CAACf,KAAK,CAAC,GAAG;0BAAE,GAAGe,WAAW,CAACf,KAAK,CAAC;0BAAEjK,EAAE,EAAE0E,CAAC,CAACkG,MAAM,CAACF;wBAAM,CAAC;wBAClE7L,iBAAiB,CAAC;0BAAE,GAAGD,cAAc;0BAAEyB,iBAAiB,EAAE2K;wBAAY,CAAC,CAAC;sBAC1E,CAAE;sBACF/C,SAAS,EAAC;oBAA2G;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACN5L,OAAA;oBAAK6L,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,eAC7C9L,OAAA;sBACEqO,IAAI,EAAC,QAAQ;sBACbrB,OAAO,EAAEA,CAAA,KAAM;wBACb,MAAM4B,WAAW,GAAG,CAAC,GAAGpM,cAAc,CAACyB,iBAAiB,CAAC;wBACzD2K,WAAW,CAACC,MAAM,CAAChB,KAAK,EAAE,CAAC,CAAC;wBAC5BpL,iBAAiB,CAAC;0BAAE,GAAGD,cAAc;0BAAEyB,iBAAiB,EAAE2K;wBAAY,CAAC,CAAC;sBAC1E,CAAE;sBACF/C,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAC5C;oBAED;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA,GAvCEiC,KAAK;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwCV,CACN,CAAC,eACF5L,OAAA;kBACEqO,IAAI,EAAC,QAAQ;kBACbrB,OAAO,EAAEA,CAAA,KAAM;oBACbvK,iBAAiB,CAAC;sBAChB,GAAGD,cAAc;sBACjByB,iBAAiB,EAAE,CAAC,GAAGzB,cAAc,CAACyB,iBAAiB,EAAE;wBAAEN,EAAE,EAAE,EAAE;wBAAEC,EAAE,EAAE;sBAAG,CAAC;oBAC7E,CAAC,CAAC;kBACJ,CAAE;kBACFiI,SAAS,EAAC,mFAAmF;kBAAAC,QAAA,EAC9F;gBAED;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAa;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrF5L,OAAA;gBAAK6L,SAAS,EAAC,kFAAkF;gBAAAC,QAAA,gBAC/F9L,OAAA;kBAAA8L,QAAA,gBACE9L,OAAA;oBAAO6L,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAwB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAChG5L,OAAA;oBACEqO,IAAI,EAAC,QAAQ;oBACbC,KAAK,EAAE9L,cAAc,CAACwC,YAAa;oBACnCuJ,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;sBACjC,GAAGD,cAAc;sBACjBwC,YAAY,EAAE8J,QAAQ,CAACxG,CAAC,CAACkG,MAAM,CAACF,KAAK;oBACvC,CAAC,CAAE;oBACHS,GAAG,EAAC,IAAI;oBACRC,IAAI,EAAC,IAAI;oBACTnD,SAAS,EAAC,2GAA2G;oBACrH4C,QAAQ;kBAAA;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5L,OAAA;kBAAA8L,QAAA,gBACE9L,OAAA;oBAAO6L,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAqB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7F5L,OAAA;oBAAK6L,SAAS,EAAC,WAAW;oBAAAC,QAAA,GACvBtJ,cAAc,CAACY,cAAc,CAACE,GAAG,CAAC,CAACC,IAAI,EAAEsK,KAAK,kBAC7C7N,OAAA;sBAAiB6L,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,gBACtD9L,OAAA;wBACEqO,IAAI,EAAC,MAAM;wBACXC,KAAK,EAAE/K,IAAK;wBACZgL,QAAQ,EAAGjG,CAAC,IAAK;0BACf,MAAM2G,QAAQ,GAAG,CAAC,GAAGzM,cAAc,CAACY,cAAc,CAAC;0BACnD6L,QAAQ,CAACpB,KAAK,CAAC,GAAGvF,CAAC,CAACkG,MAAM,CAACF,KAAK;0BAChC7L,iBAAiB,CAAC;4BAAE,GAAGD,cAAc;4BAAEY,cAAc,EAAE6L;0BAAS,CAAC,CAAC;wBACpE,CAAE;wBACFpD,SAAS,EAAC,2GAA2G;wBACrH4C,QAAQ;sBAAA;wBAAAhD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC,eACF5L,OAAA;wBACEqO,IAAI,EAAC,QAAQ;wBACbrB,OAAO,EAAEA,CAAA,KAAM;0BACb,MAAMiC,QAAQ,GAAG,CAAC,GAAGzM,cAAc,CAACY,cAAc,CAAC;0BACnD6L,QAAQ,CAACJ,MAAM,CAAChB,KAAK,EAAE,CAAC,CAAC;0BACzBpL,iBAAiB,CAAC;4BAAE,GAAGD,cAAc;4BAAEY,cAAc,EAAE6L;0BAAS,CAAC,CAAC;wBACpE,CAAE;wBACFpD,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,eAE3C9L,OAAA;0BAAKqM,KAAK,EAAC,4BAA4B;0BAACR,SAAS,EAAC,SAAS;0BAACS,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,cAAc;0BAAAT,QAAA,eACjG9L,OAAA;4BAAMwM,QAAQ,EAAC,SAAS;4BAACC,CAAC,EAAC,6MAA6M;4BAACC,QAAQ,EAAC;0BAAS;4BAAAjB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3P;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GAxBDiC,KAAK;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAyBV,CACN,CAAC,eACF5L,OAAA;sBACEqO,IAAI,EAAC,QAAQ;sBACbrB,OAAO,EAAEA,CAAA,KAAM;wBACbvK,iBAAiB,CAAC;0BAChB,GAAGD,cAAc;0BACjBY,cAAc,EAAE,CAAC,GAAGZ,cAAc,CAACY,cAAc,EAAE,OAAO;wBAC5D,CAAC,CAAC;sBACJ,CAAE;sBACFyI,SAAS,EAAC,0FAA0F;sBAAAC,QAAA,EACrG;oBAED;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAQ;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChF5L,OAAA;gBAAK6L,SAAS,EAAC,kFAAkF;gBAAAC,QAAA,EAC9F,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC,CAACxI,GAAG,CAAEuF,GAAG,iBACtF7I,OAAA;kBAAe6L,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC1C9L,OAAA;oBACEqO,IAAI,EAAC,UAAU;oBACfa,EAAE,EAAE,WAAWrG,GAAG,EAAG;oBACrBsG,OAAO,EAAE3M,cAAc,CAACyC,QAAQ,CAAC0F,QAAQ,CAAC9B,GAAG,CAAE;oBAC/C0F,QAAQ,EAAEA,CAAA,KAAM;sBACd,MAAMa,eAAe,GAAG,CAAC,GAAG5M,cAAc,CAACyC,QAAQ,CAAC;sBACpD,IAAImK,eAAe,CAACzE,QAAQ,CAAC9B,GAAG,CAAC,EAAE;wBACjC;wBACApG,iBAAiB,CAAC;0BAChB,GAAGD,cAAc;0BACjByC,QAAQ,EAAEmK,eAAe,CAACC,MAAM,CAACC,OAAO,IAAIA,OAAO,KAAKzG,GAAG;wBAC7D,CAAC,CAAC;sBACJ,CAAC,MAAM;wBACL;wBACApG,iBAAiB,CAAC;0BAChB,GAAGD,cAAc;0BACjByC,QAAQ,EAAE,CAAC,GAAGmK,eAAe,EAAEvG,GAAG;wBACpC,CAAC,CAAC;sBACJ;oBACF,CAAE;oBACFgD,SAAS,EAAC;kBAAmE;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC,eACF5L,OAAA;oBAAOuP,OAAO,EAAE,WAAW1G,GAAG,EAAG;oBAACgD,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC3EjD;kBAAG;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GAzBA/C,GAAG;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0BR,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAG6L,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAA0C;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,eAEN5L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/E5L,OAAA;gBAAK6L,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,gBACzD9L,OAAA;kBAAA8L,QAAA,gBACE9L,OAAA;oBAAO6L,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxF5L,OAAA;oBACEqO,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAE9L,cAAc,CAAC0B,OAAO,CAACC,MAAM,CAACR,EAAG;oBACxC4K,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;sBACjC,GAAGD,cAAc;sBACjB0B,OAAO,EAAE;wBACP,GAAG1B,cAAc,CAAC0B,OAAO;wBACzBC,MAAM,EAAE;0BACN,GAAG3B,cAAc,CAAC0B,OAAO,CAACC,MAAM;0BAChCR,EAAE,EAAE2E,CAAC,CAACkG,MAAM,CAACF;wBACf;sBACF;oBACF,CAAC,CAAE;oBACHzC,SAAS,EAAC;kBAA2G;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5L,OAAA;kBAAA8L,QAAA,gBACE9L,OAAA;oBAAO6L,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAe;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACvF5L,OAAA;oBACEqO,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAE9L,cAAc,CAAC0B,OAAO,CAACC,MAAM,CAACP,EAAG;oBACxC2K,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;sBACjC,GAAGD,cAAc;sBACjB0B,OAAO,EAAE;wBACP,GAAG1B,cAAc,CAAC0B,OAAO;wBACzBC,MAAM,EAAE;0BACN,GAAG3B,cAAc,CAAC0B,OAAO,CAACC,MAAM;0BAChCP,EAAE,EAAE0E,CAAC,CAACkG,MAAM,CAACF;wBACf;sBACF;oBACF,CAAC,CAAE;oBACHzC,SAAS,EAAC;kBAA2G;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5L,OAAA;kBAAA8L,QAAA,gBACE9L,OAAA;oBAAO6L,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAc;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtF5L,OAAA;oBACEqO,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAE9L,cAAc,CAAC0B,OAAO,CAACE,IAAI,CAACT,EAAG;oBACtC4K,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;sBACjC,GAAGD,cAAc;sBACjB0B,OAAO,EAAE;wBACP,GAAG1B,cAAc,CAAC0B,OAAO;wBACzBE,IAAI,EAAE;0BACJ,GAAG5B,cAAc,CAAC0B,OAAO,CAACE,IAAI;0BAC9BT,EAAE,EAAE2E,CAAC,CAACkG,MAAM,CAACF;wBACf;sBACF;oBACF,CAAC,CAAE;oBACHzC,SAAS,EAAC;kBAA2G;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5L,OAAA;kBAAA8L,QAAA,gBACE9L,OAAA;oBAAO6L,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAa;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACrF5L,OAAA;oBACEqO,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAE9L,cAAc,CAAC0B,OAAO,CAACE,IAAI,CAACR,EAAG;oBACtC2K,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;sBACjC,GAAGD,cAAc;sBACjB0B,OAAO,EAAE;wBACP,GAAG1B,cAAc,CAAC0B,OAAO;wBACzBE,IAAI,EAAE;0BACJ,GAAG5B,cAAc,CAAC0B,OAAO,CAACE,IAAI;0BAC9BR,EAAE,EAAE0E,CAAC,CAACkG,MAAM,CAACF;wBACf;sBACF;oBACF,CAAC,CAAE;oBACHzC,SAAS,EAAC;kBAA2G;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5L,OAAA;kBAAA8L,QAAA,gBACE9L,OAAA;oBAAO6L,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzF5L,OAAA;oBACEqO,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAE9L,cAAc,CAAC0B,OAAO,CAACG,OAAO,CAACV,EAAG;oBACzC4K,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;sBACjC,GAAGD,cAAc;sBACjB0B,OAAO,EAAE;wBACP,GAAG1B,cAAc,CAAC0B,OAAO;wBACzBG,OAAO,EAAE;0BACP,GAAG7B,cAAc,CAAC0B,OAAO,CAACG,OAAO;0BACjCV,EAAE,EAAE2E,CAAC,CAACkG,MAAM,CAACF;wBACf;sBACF;oBACF,CAAC,CAAE;oBACHzC,SAAS,EAAC;kBAA2G;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5L,OAAA;kBAAA8L,QAAA,gBACE9L,OAAA;oBAAO6L,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxF5L,OAAA;oBACEqO,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAE9L,cAAc,CAAC0B,OAAO,CAACG,OAAO,CAACT,EAAG;oBACzC2K,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;sBACjC,GAAGD,cAAc;sBACjB0B,OAAO,EAAE;wBACP,GAAG1B,cAAc,CAAC0B,OAAO;wBACzBG,OAAO,EAAE;0BACP,GAAG7B,cAAc,CAAC0B,OAAO,CAACG,OAAO;0BACjCT,EAAE,EAAE0E,CAAC,CAACkG,MAAM,CAACF;wBACf;sBACF;oBACF,CAAC,CAAE;oBACHzC,SAAS,EAAC;kBAA2G;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5L,OAAA;kBAAA8L,QAAA,gBACE9L,OAAA;oBAAO6L,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAW;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnF5L,OAAA;oBACEqO,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAE9L,cAAc,CAAC0B,OAAO,CAACI,UAAW;oBACzCiK,QAAQ,EAAGjG,CAAC,IAAK7F,iBAAiB,CAAC;sBACjC,GAAGD,cAAc;sBACjB0B,OAAO,EAAE;wBACP,GAAG1B,cAAc,CAAC0B,OAAO;wBACzBI,UAAU,EAAEgE,CAAC,CAACkG,MAAM,CAACF;sBACvB;oBACF,CAAC,CAAE;oBACHzC,SAAS,EAAC;kBAA2G;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5L,OAAA;cAAK6L,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9C9L,OAAA,CAACX,MAAM,CAACuN,MAAM;gBACZyB,IAAI,EAAC,QAAQ;gBACbrB,OAAO,EAAEA,CAAA,KAAM;kBAAE1M,sBAAsB,CAAC,KAAK,CAAC;kBAAE2B,QAAQ,CAAC,uBAAuB,EAAE;oBAAEM,KAAK,EAAE,CAAC;kBAAE,CAAC,CAAC;gBAAE,CAAE;gBACpGsJ,SAAS,EAAC,0GAA0G;gBACpHgB,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAAhB,QAAA,EAC3B;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,eAChB5L,OAAA,CAACX,MAAM,CAACuN,MAAM;gBACZyB,IAAI,EAAC,QAAQ;gBACbxC,SAAS,EAAC,0KAA0K;gBACpLgB,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAAhB,QAAA,EAEzBzJ,cAAc,GAAG,mBAAmB,GAAG;cAAgB;gBAAAoJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,EAEArL,eAAe,iBACdP,OAAA;MAAK6L,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F9L,OAAA,CAACX,MAAM,CAAC6M,GAAG;QACTC,OAAO,EAAE;UAAEW,KAAK,EAAE,GAAG;UAAE3B,OAAO,EAAE;QAAE,CAAE;QACpCiB,OAAO,EAAE;UAAEU,KAAK,EAAE,CAAC;UAAE3B,OAAO,EAAE;QAAE,CAAE;QAClCU,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAEzF9L,OAAA;UAAK6L,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB9L,OAAA;YAAK6L,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD9L,OAAA;cAAI6L,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAExJ,UAAU,GAAG,aAAa,GAAG;YAAY;cAAAmJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjG5L,OAAA;cAAQgN,OAAO,EAAEA,CAAA,KAAM;gBAAExM,kBAAkB,CAAC,KAAK,CAAC;gBAAEyB,QAAQ,CAAC,uBAAuB,EAAE;kBAAEM,KAAK,EAAE,CAAC;gBAAE,CAAC,CAAC;cAAE,CAAE;cAACsJ,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eACpJ9L,OAAA;gBAAKqM,KAAK,EAAC,4BAA4B;gBAACR,SAAS,EAAC,SAAS;gBAACU,IAAI,EAAC,MAAM;gBAACD,OAAO,EAAC,WAAW;gBAACkB,MAAM,EAAC,cAAc;gBAAA1B,QAAA,eAC/G9L,OAAA;kBAAMyN,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAAClB,CAAC,EAAC;gBAAsB;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN5L,OAAA;YAAMoO,QAAQ,EAAE3E,kBAAmB;YAACoC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvD9L,OAAA;cAAK6L,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD9L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAW;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnF5L,OAAA;kBACEqO,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEpJ,UAAU,CAACI,SAAU;kBAC5BiJ,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEI,SAAS,EAAEgD,CAAC,CAACkG,MAAM,CAACF;kBAAM,CAAC,CAAE;kBAC7EzC,SAAS,EAAC,2GAA2G;kBACrH4C,QAAQ;kBACRC,QAAQ,EAAEpM;gBAAW;kBAAAmJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAe;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvF5L,OAAA;kBACEqO,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEpJ,UAAU,CAACxB,IAAI,CAACC,EAAG;kBAC1B4K,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAExB,IAAI,EAAE;sBAAE,GAAGwB,UAAU,CAACxB,IAAI;sBAAEC,EAAE,EAAE2E,CAAC,CAACkG,MAAM,CAACF;oBAAM;kBAAE,CAAC,CAAE;kBACpGzC,SAAS,EAAC,2GAA2G;kBACrH4C,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAc;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtF5L,OAAA;kBACEqO,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEpJ,UAAU,CAACxB,IAAI,CAACE,EAAG;kBAC1B2K,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAExB,IAAI,EAAE;sBAAE,GAAGwB,UAAU,CAACxB,IAAI;sBAAEE,EAAE,EAAE0E,CAAC,CAACkG,MAAM,CAACF;oBAAM;kBAAE,CAAC,CAAE;kBACpGzC,SAAS,EAAC,gGAAgG;kBAC1G4C,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAsB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9F5L,OAAA;kBACEqO,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEpJ,UAAU,CAACK,UAAU,CAAC5B,EAAG;kBAChC4K,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEK,UAAU,EAAE;sBAAE,GAAGL,UAAU,CAACK,UAAU;sBAAE5B,EAAE,EAAE2E,CAAC,CAACkG,MAAM,CAACF;oBAAM;kBAAE,CAAC,CAAE;kBAChHzC,SAAS,EAAC,2GAA2G;kBACrH4C,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7F5L,OAAA;kBACEqO,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEpJ,UAAU,CAACK,UAAU,CAAC3B,EAAG;kBAChC2K,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEK,UAAU,EAAE;sBAAE,GAAGL,UAAU,CAACK,UAAU;sBAAE3B,EAAE,EAAE0E,CAAC,CAACkG,MAAM,CAACF;oBAAM;kBAAE,CAAC,CAAE;kBAChHzC,SAAS,EAAC,2GAA2G;kBACrH4C,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAe;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvF5L,OAAA;kBACEqO,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEpJ,UAAU,CAAChB,OAAO,CAACE,IAAI,CAACT,EAAG;kBAClC4K,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEhB,OAAO,EAAE;sBAAE,GAAGgB,UAAU,CAAChB,OAAO;sBAAEE,IAAI,EAAE;wBAAE,GAAGc,UAAU,CAAChB,OAAO,CAACE,IAAI;wBAAET,EAAE,EAAE2E,CAAC,CAACkG,MAAM,CAACF;sBAAM;oBAAE;kBAAE,CAAC,CAAE;kBAChJzC,SAAS,EAAC,2GAA2G;kBACrH4C,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAc;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtF5L,OAAA;kBACEqO,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEpJ,UAAU,CAAChB,OAAO,CAACE,IAAI,CAACR,EAAG;kBAClC2K,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEhB,OAAO,EAAE;sBAAE,GAAGgB,UAAU,CAAChB,OAAO;sBAAEE,IAAI,EAAE;wBAAE,GAAGc,UAAU,CAAChB,OAAO,CAACE,IAAI;wBAAER,EAAE,EAAE0E,CAAC,CAACkG,MAAM,CAACF;sBAAM;oBAAE;kBAAE,CAAC,CAAE;kBAChJzC,SAAS,EAAC,2GAA2G;kBACrH4C,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAM;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9E5L,OAAA;kBACEqO,IAAI,EAAC,KAAK;kBACVC,KAAK,EAAEpJ,UAAU,CAACX,WAAW,CAACC,KAAM;kBACpC+J,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEX,WAAW,EAAE;sBAAE,GAAGW,UAAU,CAACX,WAAW;sBAAEC,KAAK,EAAE8D,CAAC,CAACkG,MAAM,CAACF;oBAAM;kBAAE,CAAC,CAAE;kBACrHzC,SAAS,EAAC,2GAA2G;kBACrH4C,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAM;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9E5L,OAAA;kBACEqO,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAEpJ,UAAU,CAACX,WAAW,CAACE,KAAM;kBACpC8J,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEX,WAAW,EAAE;sBAAE,GAAGW,UAAU,CAACX,WAAW;sBAAEE,KAAK,EAAE6D,CAAC,CAACkG,MAAM,CAACF;oBAAM;kBAAE,CAAC,CAAE;kBACrHzC,SAAS,EAAC,2GAA2G;kBACrH4C,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxF5L,OAAA;kBACEqO,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEpJ,UAAU,CAACvC,aAAc;kBAChC4L,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEvC,aAAa,EAAE2F,CAAC,CAACkG,MAAM,CAACF;kBAAM,CAAC,CAAE;kBACjFzC,SAAS,EAAC,2GAA2G;kBACrH4C,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAc;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtF5L,OAAA;kBACEqO,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEpJ,UAAU,CAACnC,WAAY;kBAC9BwL,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEnC,WAAW,EAAEuF,CAAC,CAACkG,MAAM,CAACF;kBAAM,CAAC,CAAE;kBAC/EzC,SAAS,EAAC,2GAA2G;kBACrH4C,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,kBAAgB,eAAA9L,OAAA;kBAAM6L,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAU;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjJ5L,OAAA;gBACEsO,KAAK,EAAEpJ,UAAU,CAACM,KAAK,CAAC7B,EAAG;gBAC3B4K,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;kBAAE,GAAGD,UAAU;kBAAEM,KAAK,EAAE;oBAAE,GAAGN,UAAU,CAACM,KAAK;oBAAE7B,EAAE,EAAE2E,CAAC,CAACkG,MAAM,CAACF;kBAAM;gBAAE,CAAC,CAAE;gBACtGK,IAAI,EAAC,GAAG;gBACR9C,SAAS,EAAC;cAA2G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,iBAAe,eAAA9L,OAAA;kBAAM6L,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAU;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChJ5L,OAAA;gBACEsO,KAAK,EAAEpJ,UAAU,CAACM,KAAK,CAAC5B,EAAG;gBAC3B2K,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;kBAAE,GAAGD,UAAU;kBAAEM,KAAK,EAAE;oBAAE,GAAGN,UAAU,CAACM,KAAK;oBAAE5B,EAAE,EAAE0E,CAAC,CAACkG,MAAM,CAACF;kBAAM;gBAAE,CAAC,CAAE;gBACtGK,IAAI,EAAC,GAAG;gBACR9C,SAAS,EAAC;cAA2G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAS,eAAA9L,OAAA;kBAAM6L,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAU;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1I5L,OAAA;gBAAK6L,SAAS,EAAC,WAAW;gBAAAC,QAAA,GACvB5G,UAAU,CAACG,QAAQ,CAAC/B,GAAG,CAAC,CAACsF,OAAO,EAAEiF,KAAK,kBACtC7N,OAAA;kBAAiB6L,SAAS,EAAC,6EAA6E;kBAAAC,QAAA,gBACtG9L,OAAA;oBAAA8L,QAAA,gBACE9L,OAAA;sBAAO6L,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACzF5L,OAAA;sBACEqO,IAAI,EAAC,MAAM;sBACXC,KAAK,EAAE1F,OAAO,CAACjF,EAAG;sBAClB4K,QAAQ,EAAGjG,CAAC,IAAK;wBACf,MAAMsG,WAAW,GAAG,CAAC,GAAG1J,UAAU,CAACG,QAAQ,CAAC;wBAC5CuJ,WAAW,CAACf,KAAK,CAAC,GAAG;0BAAE,GAAGe,WAAW,CAACf,KAAK,CAAC;0BAAElK,EAAE,EAAE2E,CAAC,CAACkG,MAAM,CAACF;wBAAM,CAAC;wBAClEnJ,aAAa,CAAC;0BAAE,GAAGD,UAAU;0BAAEG,QAAQ,EAAEuJ;wBAAY,CAAC,CAAC;sBACzD,CAAE;sBACF/C,SAAS,EAAC;oBAA2G;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACN5L,OAAA;oBAAA8L,QAAA,gBACE9L,OAAA;sBAAO6L,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACxF5L,OAAA;sBACEqO,IAAI,EAAC,MAAM;sBACXC,KAAK,EAAE1F,OAAO,CAAChF,EAAG;sBAClB2K,QAAQ,EAAGjG,CAAC,IAAK;wBACf,MAAMsG,WAAW,GAAG,CAAC,GAAG1J,UAAU,CAACG,QAAQ,CAAC;wBAC5CuJ,WAAW,CAACf,KAAK,CAAC,GAAG;0BAAE,GAAGe,WAAW,CAACf,KAAK,CAAC;0BAAEjK,EAAE,EAAE0E,CAAC,CAACkG,MAAM,CAACF;wBAAM,CAAC;wBAClEnJ,aAAa,CAAC;0BAAE,GAAGD,UAAU;0BAAEG,QAAQ,EAAEuJ;wBAAY,CAAC,CAAC;sBACzD,CAAE;sBACF/C,SAAS,EAAC;oBAA2G;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACN5L,OAAA;oBAAK6L,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,eAC7C9L,OAAA;sBACEqO,IAAI,EAAC,QAAQ;sBACbrB,OAAO,EAAEA,CAAA,KAAM;wBACb,MAAM4B,WAAW,GAAG,CAAC,GAAG1J,UAAU,CAACG,QAAQ,CAAC;wBAC5CuJ,WAAW,CAACC,MAAM,CAAChB,KAAK,EAAE,CAAC,CAAC;wBAC5B1I,aAAa,CAAC;0BAAE,GAAGD,UAAU;0BAAEG,QAAQ,EAAEuJ;wBAAY,CAAC,CAAC;sBACzD,CAAE;sBACF/C,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAC5C;oBAED;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA,GAvCEiC,KAAK;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwCV,CACN,CAAC,eACF5L,OAAA;kBACEqO,IAAI,EAAC,QAAQ;kBACbrB,OAAO,EAAEA,CAAA,KAAM;oBACb7H,aAAa,CAAC;sBACZ,GAAGD,UAAU;sBACbG,QAAQ,EAAE,CAAC,GAAGH,UAAU,CAACG,QAAQ,EAAE;wBAAE1B,EAAE,EAAE,EAAE;wBAAEC,EAAE,EAAE;sBAAG,CAAC;oBACvD,CAAC,CAAC;kBACJ,CAAE;kBACFiI,SAAS,EAAC,mFAAmF;kBAAAC,QAAA,EAC9F;gBAED;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAa;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrF5L,OAAA;gBAAK6L,SAAS,EAAC,kFAAkF;gBAAAC,QAAA,gBAC/F9L,OAAA;kBAAA8L,QAAA,gBACE9L,OAAA;oBAAO6L,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAwB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAChG5L,OAAA;oBACEqO,IAAI,EAAC,QAAQ;oBACbC,KAAK,EAAEpJ,UAAU,CAACF,YAAa;oBAC/BuJ,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;sBAC7B,GAAGD,UAAU;sBACbF,YAAY,EAAE8J,QAAQ,CAACxG,CAAC,CAACkG,MAAM,CAACF,KAAK;oBACvC,CAAC,CAAE;oBACHS,GAAG,EAAC,IAAI;oBACRC,IAAI,EAAC,IAAI;oBACTnD,SAAS,EAAC,2GAA2G;oBACrH4C,QAAQ;kBAAA;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5L,OAAA;kBAAA8L,QAAA,gBACE9L,OAAA;oBAAO6L,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAqB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7F5L,OAAA;oBAAK6L,SAAS,EAAC,WAAW;oBAAAC,QAAA,GACvB5G,UAAU,CAAC9B,cAAc,CAACE,GAAG,CAAC,CAACC,IAAI,EAAEsK,KAAK,kBACzC7N,OAAA;sBAAiB6L,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,gBACtD9L,OAAA;wBACEqO,IAAI,EAAC,MAAM;wBACXC,KAAK,EAAE/K,IAAK;wBACZgL,QAAQ,EAAGjG,CAAC,IAAK;0BACf,MAAM2G,QAAQ,GAAG,CAAC,GAAG/J,UAAU,CAAC9B,cAAc,CAAC;0BAC/C6L,QAAQ,CAACpB,KAAK,CAAC,GAAGvF,CAAC,CAACkG,MAAM,CAACF,KAAK;0BAChCnJ,aAAa,CAAC;4BAAE,GAAGD,UAAU;4BAAE9B,cAAc,EAAE6L;0BAAS,CAAC,CAAC;wBAC5D,CAAE;wBACFpD,SAAS,EAAC,2GAA2G;wBACrH4C,QAAQ;sBAAA;wBAAAhD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC,eACF5L,OAAA;wBACEqO,IAAI,EAAC,QAAQ;wBACbrB,OAAO,EAAEA,CAAA,KAAM;0BACb,MAAMiC,QAAQ,GAAG,CAAC,GAAG/J,UAAU,CAAC9B,cAAc,CAAC;0BAC/C6L,QAAQ,CAACJ,MAAM,CAAChB,KAAK,EAAE,CAAC,CAAC;0BACzB1I,aAAa,CAAC;4BAAE,GAAGD,UAAU;4BAAE9B,cAAc,EAAE6L;0BAAS,CAAC,CAAC;wBAC5D,CAAE;wBACFpD,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,eAE3C9L,OAAA;0BAAKqM,KAAK,EAAC,4BAA4B;0BAACR,SAAS,EAAC,SAAS;0BAACS,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,cAAc;0BAAAT,QAAA,eACjG9L,OAAA;4BAAMwM,QAAQ,EAAC,SAAS;4BAACC,CAAC,EAAC,6MAA6M;4BAACC,QAAQ,EAAC;0BAAS;4BAAAjB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3P;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GAxBDiC,KAAK;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAyBV,CACN,CAAC,eACF5L,OAAA;sBACEqO,IAAI,EAAC,QAAQ;sBACbrB,OAAO,EAAEA,CAAA,KAAM;wBACb7H,aAAa,CAAC;0BACZ,GAAGD,UAAU;0BACb9B,cAAc,EAAE,CAAC,GAAG8B,UAAU,CAAC9B,cAAc,EAAE,OAAO;wBACxD,CAAC,CAAC;sBACJ,CAAE;sBACFyI,SAAS,EAAC,0FAA0F;sBAAAC,QAAA,EACrG;oBAED;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAa;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrF5L,OAAA;gBAAK6L,SAAS,EAAC,kFAAkF;gBAAAC,QAAA,EAC9F0D,MAAM,CAACC,OAAO,CAACvK,UAAU,CAACO,YAAY,CAAC,CAACnC,GAAG,CAAC,CAAC,CAACuF,GAAG,EAAE6G,KAAK,CAAC,kBACxD1P,OAAA;kBAAe6L,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBACtC9L,OAAA;oBAAO6L,SAAS,EAAC,yDAAyD;oBAAAC,QAAA,EAAEjD;kBAAG;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxF5L,OAAA;oBACEqO,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAEoB,KAAM;oBACbnB,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;sBAC7B,GAAGD,UAAU;sBACbO,YAAY,EAAE;wBACZ,GAAGP,UAAU,CAACO,YAAY;wBAC1B,CAACoD,GAAG,GAAGP,CAAC,CAACkG,MAAM,CAACF;sBAClB;oBACF,CAAC,CAAE;oBACHqB,WAAW,EAAC,uBAAuB;oBACnC9D,SAAS,EAAC;kBAA2G;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtH,CAAC;gBAAA,GAdM/C,GAAG;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAeR,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,UAAQ,eAAA9L,OAAA;kBAAM6L,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtJ5L,OAAA;gBAAK6L,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,gBACzD9L,OAAA;kBAAA8L,QAAA,gBACE9L,OAAA;oBAAO6L,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxF5L,OAAA;oBACEqO,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAEpJ,UAAU,CAAChB,OAAO,CAACC,MAAM,CAACR,EAAG;oBACpC4K,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;sBAC7B,GAAGD,UAAU;sBACbhB,OAAO,EAAE;wBACP,GAAGgB,UAAU,CAAChB,OAAO;wBACrBC,MAAM,EAAE;0BACN,GAAGe,UAAU,CAAChB,OAAO,CAACC,MAAM;0BAC5BR,EAAE,EAAE2E,CAAC,CAACkG,MAAM,CAACF;wBACf;sBACF;oBACF,CAAC,CAAE;oBACHzC,SAAS,EAAC;kBAA2G;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5L,OAAA;kBAAA8L,QAAA,gBACE9L,OAAA;oBAAO6L,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAe;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACvF5L,OAAA;oBACEqO,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAEpJ,UAAU,CAAChB,OAAO,CAACC,MAAM,CAACP,EAAG;oBACpC2K,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;sBAC7B,GAAGD,UAAU;sBACbhB,OAAO,EAAE;wBACP,GAAGgB,UAAU,CAAChB,OAAO;wBACrBC,MAAM,EAAE;0BACN,GAAGe,UAAU,CAAChB,OAAO,CAACC,MAAM;0BAC5BP,EAAE,EAAE0E,CAAC,CAACkG,MAAM,CAACF;wBACf;sBACF;oBACF,CAAC,CAAE;oBACHzC,SAAS,EAAC;kBAA2G;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5L,OAAA;kBAAA8L,QAAA,gBACE9L,OAAA;oBAAO6L,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzF5L,OAAA;oBACEqO,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAEpJ,UAAU,CAAChB,OAAO,CAACG,OAAO,CAACV,EAAG;oBACrC4K,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;sBAC7B,GAAGD,UAAU;sBACbhB,OAAO,EAAE;wBACP,GAAGgB,UAAU,CAAChB,OAAO;wBACrBG,OAAO,EAAE;0BACP,GAAGa,UAAU,CAAChB,OAAO,CAACG,OAAO;0BAC7BV,EAAE,EAAE2E,CAAC,CAACkG,MAAM,CAACF;wBACf;sBACF;oBACF,CAAC,CAAE;oBACHzC,SAAS,EAAC;kBAA2G;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5L,OAAA;kBAAA8L,QAAA,gBACE9L,OAAA;oBAAO6L,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxF5L,OAAA;oBACEqO,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAEpJ,UAAU,CAAChB,OAAO,CAACG,OAAO,CAACT,EAAG;oBACrC2K,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;sBAC7B,GAAGD,UAAU;sBACbhB,OAAO,EAAE;wBACP,GAAGgB,UAAU,CAAChB,OAAO;wBACrBG,OAAO,EAAE;0BACP,GAAGa,UAAU,CAAChB,OAAO,CAACG,OAAO;0BAC7BT,EAAE,EAAE0E,CAAC,CAACkG,MAAM,CAACF;wBACf;sBACF;oBACF,CAAC,CAAE;oBACHzC,SAAS,EAAC;kBAA2G;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5L,OAAA;kBAAA8L,QAAA,gBACE9L,OAAA;oBAAO6L,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAW;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnF5L,OAAA;oBACEqO,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAEpJ,UAAU,CAAChB,OAAO,CAACI,UAAW;oBACrCiK,QAAQ,EAAGjG,CAAC,IAAKnD,aAAa,CAAC;sBAC7B,GAAGD,UAAU;sBACbhB,OAAO,EAAE;wBACP,GAAGgB,UAAU,CAAChB,OAAO;wBACrBI,UAAU,EAAEgE,CAAC,CAACkG,MAAM,CAACF;sBACvB;oBACF,CAAC,CAAE;oBACHzC,SAAS,EAAC;kBAA2G;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5L,OAAA;cAAK6L,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9C9L,OAAA,CAACX,MAAM,CAACuN,MAAM;gBACZyB,IAAI,EAAC,QAAQ;gBACbrB,OAAO,EAAEA,CAAA,KAAM;kBAAExM,kBAAkB,CAAC,KAAK,CAAC;kBAAEyB,QAAQ,CAAC,uBAAuB,EAAE;oBAAEM,KAAK,EAAE,CAAC;kBAAE,CAAC,CAAC;gBAAE,CAAE;gBAChGsJ,SAAS,EAAC,0GAA0G;gBACpHgB,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAAhB,QAAA,EAC3B;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,eAChB5L,OAAA,CAACX,MAAM,CAACuN,MAAM;gBACZyB,IAAI,EAAC,QAAQ;gBACbxC,SAAS,EAAC,0KAA0K;gBACpLgB,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAAhB,QAAA,EAEzBxJ,UAAU,GAAG,eAAe,GAAG;cAAY;gBAAAmJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,EAEAnL,gBAAgB,iBACfT,OAAA;MAAK6L,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F9L,OAAA,CAACX,MAAM,CAAC6M,GAAG;QACTC,OAAO,EAAE;UAAEW,KAAK,EAAE,GAAG;UAAE3B,OAAO,EAAE;QAAE,CAAE;QACpCiB,OAAO,EAAE;UAAEU,KAAK,EAAE,CAAC;UAAE3B,OAAO,EAAE;QAAE,CAAE;QAClCU,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAEzF9L,OAAA;UAAK6L,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB9L,OAAA;YAAK6L,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD9L,OAAA;cAAI6L,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAW;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjE5L,OAAA;cAAQgN,OAAO,EAAEA,CAAA,KAAMtM,mBAAmB,CAAC,KAAK,CAAE;cAACmL,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eAC9F9L,OAAA;gBAAKqM,KAAK,EAAC,4BAA4B;gBAACR,SAAS,EAAC,SAAS;gBAACU,IAAI,EAAC,MAAM;gBAACD,OAAO,EAAC,WAAW;gBAACkB,MAAM,EAAC,cAAc;gBAAA1B,QAAA,eAC/G9L,OAAA;kBAAMyN,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAAClB,CAAC,EAAC;gBAAsB;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN5L,OAAA;YAAMoO,QAAQ,EAAE1D,mBAAoB;YAACmB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxD9L,OAAA;cAAK6L,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD9L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAM;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9E5L,OAAA;kBACEqO,IAAI,EAAC,OAAO;kBACZ3K,IAAI,EAAC,OAAO;kBACZ4K,KAAK,EAAErI,WAAW,CAACxB,KAAM;kBACzB8J,QAAQ,EAAGjG,CAAC,IAAKpC,cAAc,CAAC;oBAAE,GAAGD,WAAW;oBAAExB,KAAK,EAAE6D,CAAC,CAACkG,MAAM,CAACF;kBAAM,CAAC,CAAE;kBAC3EzC,SAAS,EAAC,2GAA2G;kBACrH4C,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAS;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjF5L,OAAA;kBACEqO,IAAI,EAAC,UAAU;kBACf3K,IAAI,EAAC,UAAU;kBACf4K,KAAK,EAAErI,WAAW,CAACE,QAAS;kBAC5BoI,QAAQ,EAAGjG,CAAC,IAAKpC,cAAc,CAAC;oBAAE,GAAGD,WAAW;oBAAEE,QAAQ,EAAEmC,CAAC,CAACkG,MAAM,CAACF;kBAAM,CAAC,CAAE;kBAC9EzC,SAAS,EAAC,2GAA2G;kBACrH4C,QAAQ;kBACRmB,SAAS,EAAC;gBAAG;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eACF5L,OAAA;kBAAG6L,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAA2C;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAK;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7E5L,OAAA;kBACEqO,IAAI,EAAC,MAAM;kBACX3K,IAAI,EAAC,MAAM;kBACX4K,KAAK,EAAErI,WAAW,CAACvC,IAAK;kBACxB6K,QAAQ,EAAGjG,CAAC,IAAKpC,cAAc,CAAC;oBAAE,GAAGD,WAAW;oBAAEvC,IAAI,EAAE4E,CAAC,CAACkG,MAAM,CAACF;kBAAM,CAAC,CAAE;kBAC1EzC,SAAS,EAAC,2GAA2G;kBACrH4C,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5L,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAK;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7E5L,OAAA;kBACE0D,IAAI,EAAC,MAAM;kBACX4K,KAAK,EAAErI,WAAW,CAACG,IAAK;kBACxBmI,QAAQ,EAAGjG,CAAC,IAAKpC,cAAc,CAAC;oBAAE,GAAGD,WAAW;oBAAEG,IAAI,EAAEkC,CAAC,CAACkG,MAAM,CAACF;kBAAM,CAAC,CAAE;kBAC1EzC,SAAS,EAAC,2GAA2G;kBACrH4C,QAAQ;kBAAA3C,QAAA,gBAER9L,OAAA;oBAAQsO,KAAK,EAAC,SAAS;oBAAAxC,QAAA,EAAC;kBAAO;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxC5L,OAAA;oBAAQsO,KAAK,EAAC,YAAY;oBAAAxC,QAAA,EAAC;kBAAU;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9C5L,OAAA;oBAAQsO,KAAK,EAAC,OAAO;oBAAAxC,QAAA,EAAC;kBAAK;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpC5L,OAAA;oBAAQsO,KAAK,EAAC,WAAW;oBAAAxC,QAAA,EAAC;kBAAS;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5C5L,OAAA;oBAAQsO,KAAK,EAAC,SAAS;oBAAAxC,QAAA,EAAC;kBAAO;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EACL3F,WAAW,CAACG,IAAI,KAAK,SAAS,iBAC7BpG,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAW;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnF5L,OAAA;kBACEqO,IAAI,EAAC,MAAM;kBACX3K,IAAI,EAAC,WAAW;kBAChB4K,KAAK,EAAErI,WAAW,CAACI,SAAU;kBAC7BkI,QAAQ,EAAGjG,CAAC,IAAKpC,cAAc,CAAC;oBAAE,GAAGD,WAAW;oBAAEI,SAAS,EAAEiC,CAAC,CAACkG,MAAM,CAACF;kBAAM,CAAC,CAAE;kBAC/EzC,SAAS,EAAC,2GAA2G;kBACrH4C,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,EACA,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,CAAC,CAACjB,QAAQ,CAAC1E,WAAW,CAACG,IAAI,CAAC,iBACzEpG,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAW;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnF5L,OAAA;kBACE0D,IAAI,EAAC,cAAc;kBACnB4K,KAAK,EAAErI,WAAW,CAACxC,YAAa;kBAChC8K,QAAQ,EAAGjG,CAAC,IAAKpC,cAAc,CAAC;oBAAE,GAAGD,WAAW;oBAAExC,YAAY,EAAE6E,CAAC,CAACkG,MAAM,CAACF;kBAAM,CAAC,CAAE;kBAClFzC,SAAS,EAAC,2GAA2G;kBACrH4C,QAAQ;kBAAA3C,QAAA,gBAER9L,OAAA;oBAAQsO,KAAK,EAAC,EAAE;oBAAAxC,QAAA,EAAC;kBAAiB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC1CzK,YAAY,CAACmC,GAAG,CAAEqD,UAAU,iBAC3B3G,OAAA;oBAAsCsO,KAAK,EAAE3H,UAAU,CAAClD,YAAa;oBAAAqI,QAAA,EAClEnF,UAAU,CAACjD,IAAI,CAACC;kBAAE,GADRgD,UAAU,CAAClD,YAAY;oBAAAgI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAE5B,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN,EACA3F,WAAW,CAACG,IAAI,KAAK,WAAW,iBAC/BpG,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAO6L,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChF5L,OAAA;kBACE0D,IAAI,EAAC,WAAW;kBAChB4K,KAAK,EAAErI,WAAW,CAACX,SAAU;kBAC7BiJ,QAAQ,EAAGjG,CAAC,IAAKpC,cAAc,CAAC;oBAAE,GAAGD,WAAW;oBAAEX,SAAS,EAAEgD,CAAC,CAACkG,MAAM,CAACF;kBAAM,CAAC,CAAE;kBAC/EzC,SAAS,EAAC,2GAA2G;kBACrH4C,QAAQ;kBAAA3C,QAAA,gBAER9L,OAAA;oBAAQsO,KAAK,EAAC,EAAE;oBAAAxC,QAAA,EAAC;kBAAc;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACvCvK,OAAO,CAACiC,GAAG,CAAEuM,MAAM;oBAAA,IAAAC,YAAA;oBAAA,oBAClB9P,OAAA;sBAA+BsO,KAAK,EAAEuB,MAAM,CAACvK,SAAU;sBAAAwG,QAAA,EACpD,EAAAgE,YAAA,GAAAD,MAAM,CAACnM,IAAI,cAAAoM,YAAA,uBAAXA,YAAA,CAAanM,EAAE,KAAIkM,MAAM,CAACvK;oBAAS,GADzBuK,MAAM,CAACvK,SAAS;sBAAAmG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAErB,CAAC;kBAAA,CACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN5L,OAAA;cAAK6L,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9C9L,OAAA,CAACX,MAAM,CAACuN,MAAM;gBACZyB,IAAI,EAAC,QAAQ;gBACbrB,OAAO,EAAEA,CAAA,KAAMtM,mBAAmB,CAAC,KAAK,CAAE;gBAC1CmL,SAAS,EAAC,0GAA0G;gBACpHgB,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAAhB,QAAA,EAC3B;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,eAChB5L,OAAA,CAACX,MAAM,CAACuN,MAAM;gBACZyB,IAAI,EAAC,QAAQ;gBACbxC,SAAS,EAAC,0KAA0K;gBACpLgB,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAAhB,QAAA,EAC3B;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,EAEAjL,aAAa,iBACZX,OAAA;MAAK6L,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F9L,OAAA,CAACX,MAAM,CAAC6M,GAAG;QACTC,OAAO,EAAE;UAAEW,KAAK,EAAE,GAAG;UAAE3B,OAAO,EAAE;QAAE,CAAE;QACpCiB,OAAO,EAAE;UAAEU,KAAK,EAAE,CAAC;UAAE3B,OAAO,EAAE;QAAE,CAAE;QAClCU,SAAS,EAAC,8EAA8E;QAAAC,QAAA,eAExF9L,OAAA;UAAK6L,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB9L,OAAA;YAAK6L,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD9L,OAAA;cAAI6L,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAS;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/D5L,OAAA;cAAQgN,OAAO,EAAEA,CAAA,KAAMpM,gBAAgB,CAAC,KAAK,CAAE;cAACiL,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eAC3F9L,OAAA;gBAAKqM,KAAK,EAAC,4BAA4B;gBAACR,SAAS,EAAC,SAAS;gBAACU,IAAI,EAAC,MAAM;gBAACD,OAAO,EAAC,WAAW;gBAACkB,MAAM,EAAC,cAAc;gBAAA1B,QAAA,eAC/G9L,OAAA;kBAAMyN,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAAClB,CAAC,EAAC;gBAAsB;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN5L,OAAA;YAAMoO,QAAQ,EAAEtD,gBAAiB;YAACe,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACrD9L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAM;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9E5L,OAAA;gBACEqO,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAEhI,QAAQ,CAACE,KAAM;gBACtB+H,QAAQ,EAAGjG,CAAC,IAAK/B,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEE,KAAK,EAAE8B,CAAC,CAACkG,MAAM,CAACF;gBAAM,CAAC,CAAE;gBACrEzC,SAAS,EAAC,2GAA2G;gBACrH4C,QAAQ;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAQ;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChF5L,OAAA;gBACEsO,KAAK,EAAEhI,QAAQ,CAACG,OAAQ;gBACxB8H,QAAQ,EAAGjG,CAAC,IAAK/B,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEG,OAAO,EAAE6B,CAAC,CAACkG,MAAM,CAACF;gBAAM,CAAC,CAAE;gBACvEK,IAAI,EAAC,GAAG;gBACR9C,SAAS,EAAC,2GAA2G;gBACrH4C,QAAQ;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5L,OAAA;cAAK6L,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC9L,OAAA;gBACEqO,IAAI,EAAC,UAAU;gBACfa,EAAE,EAAC,UAAU;gBACbC,OAAO,EAAE7I,QAAQ,CAACI,QAAS;gBAC3B6H,QAAQ,EAAGjG,CAAC,IAAK/B,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEI,QAAQ,EAAE4B,CAAC,CAACkG,MAAM,CAACW;gBAAQ,CAAC,CAAE;gBAC1EtD,SAAS,EAAC;cAAmE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACF5L,OAAA;gBAAOuP,OAAO,EAAC,UAAU;gBAAC1D,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAEvE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,EACL,CAACtF,QAAQ,CAACI,QAAQ,iBACjB1G,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO6L,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAc;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtF5L,OAAA;gBACEsO,KAAK,EAAEhI,QAAQ,CAACK,UAAW;gBAC3B4H,QAAQ,EAAGjG,CAAC,IAAK/B,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEK,UAAU,EAAE2B,CAAC,CAACkG,MAAM,CAACF;gBAAM,CAAC,CAAE;gBAC1EzC,SAAS,EAAC,2GAA2G;gBACrH4C,QAAQ,EAAE,CAACnI,QAAQ,CAACI,QAAS;gBAAAoF,QAAA,gBAE7B9L,OAAA;kBAAQsO,KAAK,EAAC,EAAE;kBAAAxC,QAAA,EAAC;gBAAiB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC1CzK,YAAY,CAACmC,GAAG,CAAEqD,UAAU,iBAC3B3G,OAAA;kBAAsCsO,KAAK,EAAE3H,UAAU,CAAClD,YAAa;kBAAAqI,QAAA,EAClEnF,UAAU,CAACjD,IAAI,CAACC;gBAAE,GADRgD,UAAU,CAAClD,YAAY;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE5B,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,eACD5L,OAAA;cAAK6L,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9C9L,OAAA,CAACX,MAAM,CAACuN,MAAM;gBACZyB,IAAI,EAAC,QAAQ;gBACbrB,OAAO,EAAEA,CAAA,KAAMpM,gBAAgB,CAAC,KAAK,CAAE;gBACvCiL,SAAS,EAAC,0GAA0G;gBACpHgB,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAAhB,QAAA,EAC3B;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,eAChB5L,OAAA,CAACX,MAAM,CAACuN,MAAM;gBACZyB,IAAI,EAAC,QAAQ;gBACbxC,SAAS,EAAC,0KAA0K;gBACpLgB,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAAhB,QAAA,EAC3B;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,eAGD5L,OAAA,CAACJ,YAAY;MACXmM,MAAM,EAAElL,gBAAiB;MACzBkP,OAAO,EAAEA,CAAA,KAAMjP,mBAAmB,CAAC,KAAK,CAAE;MAC1C0F,KAAK,EAAEvF,YAAa;MACpBiH,OAAO,EAAEnH;IAAe;MAAA0K,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC1L,EAAA,CA16DID,mBAAmB;EAAA,QAcNf,WAAW,EACJW,OAAO,EACdV,WAAW;AAAA;AAAA6Q,EAAA,GAhBxB/P,mBAAmB;AA46DzB,eAAeA,mBAAmB;AAAC,IAAA+P,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}