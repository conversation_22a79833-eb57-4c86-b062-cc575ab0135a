{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\superadmin\\\\Dashboard.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport axios from 'axios';\nimport { motion } from 'framer-motion';\nimport { FaUniversity, FaUserPlus, FaNewspaper, FaUsers } from 'react-icons/fa';\nimport Navbar from '../student/Navbar';\nimport Loader from '../components/Loader';\nimport SuccessModal from '../components/SuccessModal';\nimport { useAuth } from '../context/AuthContext';\nimport SuperAdminSidebar from './SuperAdminSidebar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SuperAdminDashboard = () => {\n  _s();\n  var _analytics$recentActi;\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [showUniversityModal, setShowUniversityModal] = useState(false);\n  const [showAccountModal, setShowAccountModal] = useState(false);\n  const [showNewsModal, setShowNewsModal] = useState(false);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [successTitle, setSuccessTitle] = useState('Success');\n  const [universities, setUniversities] = useState([]);\n  const [analytics, setAnalytics] = useState({\n    totalUniversities: 0,\n    totalAccounts: 0,\n    recentActivity: []\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  const {\n    user,\n    token\n  } = useAuth();\n  const location = useLocation();\n  const {\n    editUniversity\n  } = location.state || {};\n\n  // University form state\n  const [universityForm, setUniversityForm] = useState({\n    universityId: '',\n    name: {\n      en: '',\n      ar: ''\n    },\n    address: {\n      en: '',\n      ar: ''\n    },\n    contactInfo: {\n      phone: '',\n      email: '',\n      website: ''\n    },\n    description: {\n      en: '',\n      ar: ''\n    },\n    establishedYear: '',\n    accreditation: {\n      en: '',\n      ar: ''\n    },\n    faculties: [],\n    programs: [],\n    studentCapacity: '',\n    tuitionFees: {\n      local: '',\n      international: ''\n    },\n    admissionRequirements: {\n      en: '',\n      ar: ''\n    },\n    campusSize: '',\n    numberOfFaculty: '',\n    researchCenters: [],\n    partnerships: [],\n    rankings: {\n      national: '',\n      international: ''\n    },\n    facilities: [],\n    scholarships: [],\n    languages: [],\n    semesterSystem: 'semester',\n    academicCalendar: {\n      fallStart: '',\n      fallEnd: '',\n      springStart: '',\n      springEnd: '',\n      summerStart: '',\n      summerEnd: ''\n    },\n    holidays: ['Friday', 'Sunday']\n  });\n\n  // Account form state\n  const [accountForm, setAccountForm] = useState({\n    email: '',\n    password: '',\n    name: '',\n    role: '',\n    studentId: '',\n    universityId: ''\n  });\n\n  // News form state\n  const [newsForm, setNewsForm] = useState({\n    title: {\n      en: '',\n      ar: ''\n    },\n    content: {\n      en: '',\n      ar: ''\n    },\n    category: '',\n    priority: 'medium',\n    targetAudience: 'all',\n    publishDate: new Date().toISOString().split('T')[0],\n    expiryDate: '',\n    isActive: true\n  });\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!user || !token) {\n        navigate('/login');\n        return;\n      }\n      try {\n        const config = {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        };\n        const [universitiesRes, analyticsRes] = await Promise.all([axios.get(`${process.env.REACT_APP_API_URL}/api/universities`, config), axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/superadmin`, config)]);\n        setUniversities(universitiesRes.data || []);\n        setAnalytics(analyticsRes.data || {\n          totalUniversities: 0,\n          totalAccounts: 0,\n          recentActivity: []\n        });\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response3$data, _err$response4;\n        console.error('Fetch error:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n        const errorMessage = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.status) === 401 ? 'Unauthorized. Please log in again.' : ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || 'Failed to load data';\n        setError(errorMessage);\n        if (((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 401) {\n          navigate('/login');\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n    if (editUniversity) setShowUniversityModal(true);\n  }, [user, token, navigate, editUniversity]);\n  const handleUniversitySubmit = async e => {\n    e.preventDefault();\n    try {\n      if (!universityForm.universityId.trim()) {\n        setError('University ID is required');\n        return;\n      }\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      if (editUniversity) {\n        await axios.put(`${process.env.REACT_APP_API_URL}/api/universities/${universityForm.universityId}`, universityForm, config);\n        setUniversities(universities.map(u => u.universityId === universityForm.universityId ? {\n          ...u,\n          ...universityForm\n        } : u));\n        setSuccessTitle('University Updated');\n        setSuccessMessage('University has been updated successfully!');\n      } else {\n        const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/universities`, universityForm, config);\n        setUniversities([...universities, response.data]);\n        setSuccessTitle('University Added');\n        setSuccessMessage('University has been added successfully!');\n      }\n      setShowSuccessModal(true);\n      setShowUniversityModal(false);\n\n      // Reset form\n      setUniversityForm({\n        universityId: '',\n        name: {\n          en: '',\n          ar: ''\n        },\n        address: {\n          en: '',\n          ar: ''\n        },\n        contactInfo: {\n          phone: '',\n          email: '',\n          website: ''\n        },\n        description: {\n          en: '',\n          ar: ''\n        },\n        establishedYear: '',\n        accreditation: {\n          en: '',\n          ar: ''\n        },\n        faculties: [],\n        programs: [],\n        studentCapacity: '',\n        tuitionFees: {\n          local: '',\n          international: ''\n        },\n        admissionRequirements: {\n          en: '',\n          ar: ''\n        },\n        campusSize: '',\n        numberOfFaculty: '',\n        researchCenters: [],\n        partnerships: [],\n        rankings: {\n          national: '',\n          international: ''\n        },\n        facilities: [],\n        scholarships: [],\n        languages: [],\n        semesterSystem: 'semester',\n        academicCalendar: {\n          fallStart: '',\n          fallEnd: '',\n          springStart: '',\n          springEnd: '',\n          summerStart: '',\n          summerEnd: ''\n        },\n        holidays: ['Friday', 'Sunday']\n      });\n\n      // Refresh analytics\n      const analyticsRes = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/superadmin`, config);\n      setAnalytics(analyticsRes.data || {\n        totalUniversities: 0,\n        totalAccounts: 0,\n        recentActivity: []\n      });\n      navigate('/superadmin/dashboard', {\n        state: {}\n      });\n    } catch (err) {\n      var _err$response5, _err$response6, _err$response6$data;\n      console.error('University submission error:', err);\n      const errorMessage = ((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : _err$response5.status) === 401 ? 'Unauthorized. Please log in again.' : ((_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.message) || 'Failed to save university';\n      setError(errorMessage);\n    }\n  };\n  const handleAccountSubmit = async e => {\n    e.preventDefault();\n    try {\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      await axios.post(`${process.env.REACT_APP_API_URL}/api/accounts`, accountForm, config);\n      setShowAccountModal(false);\n      setAccountForm({\n        email: '',\n        password: '',\n        name: '',\n        role: '',\n        studentId: '',\n        universityId: ''\n      });\n      setSuccessTitle('Account Created');\n      setSuccessMessage('Account has been created successfully!');\n      setShowSuccessModal(true);\n\n      // Refresh analytics\n      const analyticsRes = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/superadmin`, config);\n      setAnalytics(analyticsRes.data || {\n        totalUniversities: 0,\n        totalAccounts: 0,\n        recentActivity: []\n      });\n    } catch (err) {\n      var _err$response7, _err$response7$data;\n      console.error('Account creation error:', err);\n      setError(((_err$response7 = err.response) === null || _err$response7 === void 0 ? void 0 : (_err$response7$data = _err$response7.data) === null || _err$response7$data === void 0 ? void 0 : _err$response7$data.message) || 'Failed to add account');\n    }\n  };\n  const handleNewsSubmit = async e => {\n    e.preventDefault();\n    try {\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      await axios.post(`${process.env.REACT_APP_API_URL}/api/news`, newsForm, config);\n      setShowNewsModal(false);\n      setNewsForm({\n        title: {\n          en: '',\n          ar: ''\n        },\n        content: {\n          en: '',\n          ar: ''\n        },\n        category: '',\n        priority: 'medium',\n        targetAudience: 'all',\n        publishDate: new Date().toISOString().split('T')[0],\n        expiryDate: '',\n        isActive: true\n      });\n      setSuccessTitle('News Published');\n      setSuccessMessage('News has been published successfully!');\n      setShowSuccessModal(true);\n    } catch (err) {\n      var _err$response8, _err$response8$data;\n      console.error('News creation error:', err);\n      setError(((_err$response8 = err.response) === null || _err$response8 === void 0 ? void 0 : (_err$response8$data = _err$response8.data) === null || _err$response8$data === void 0 ? void 0 : _err$response8$data.message) || 'Failed to publish news');\n    }\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 248,\n    columnNumber: 23\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SuperAdminSidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"lg:ml-64 pt-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900 mb-2\",\n            children: \"Superadmin Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Manage universities, accounts, and system-wide settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            whileHover: {\n              scale: 1.05\n            },\n            className: \"bg-white p-6 rounded-xl shadow-lg border border-gray-100 cursor-pointer\",\n            onClick: () => setShowUniversityModal(true),\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 bg-blue-100 rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(FaUniversity, {\n                  className: \"h-6 w-6 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: \"Add University\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600\",\n                  children: \"Create new university\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            whileHover: {\n              scale: 1.05\n            },\n            className: \"bg-white p-6 rounded-xl shadow-lg border border-gray-100 cursor-pointer\",\n            onClick: () => setShowAccountModal(true),\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 bg-green-100 rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(FaUserPlus, {\n                  className: \"h-6 w-6 text-green-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: \"Add Account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600\",\n                  children: \"Create user account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            whileHover: {\n              scale: 1.05\n            },\n            className: \"bg-white p-6 rounded-xl shadow-lg border border-gray-100 cursor-pointer\",\n            onClick: () => setShowNewsModal(true),\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 bg-purple-100 rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(FaNewspaper, {\n                  className: \"h-6 w-6 text-purple-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: \"Publish News\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600\",\n                  children: \"Create news article\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            whileHover: {\n              scale: 1.05\n            },\n            className: \"bg-white p-6 rounded-xl shadow-lg border border-gray-100 cursor-pointer\",\n            onClick: () => navigate('/superadmin/accounts'),\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 bg-orange-100 rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(FaUsers, {\n                  className: \"h-6 w-6 text-orange-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: \"Manage Accounts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600\",\n                  children: \"View all accounts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white p-6 rounded-xl shadow-lg border border-gray-100\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 text-sm\",\n                  children: \"Total Universities\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-3xl font-bold text-blue-600\",\n                  children: analytics.totalUniversities || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FaUniversity, {\n                className: \"h-8 w-8 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white p-6 rounded-xl shadow-lg border border-gray-100\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 text-sm\",\n                  children: \"Total Accounts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-3xl font-bold text-green-600\",\n                  children: analytics.totalAccounts || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FaUsers, {\n                className: \"h-8 w-8 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white p-6 rounded-xl shadow-lg border border-gray-100\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 text-sm\",\n                  children: \"Recent Activity\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-3xl font-bold text-purple-600\",\n                  children: ((_analytics$recentActi = analytics.recentActivity) === null || _analytics$recentActi === void 0 ? void 0 : _analytics$recentActi.length) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FaNewspaper, {\n                className: \"h-8 w-8 text-purple-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-lg border border-gray-100 p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900 mb-4\",\n            children: \"Recent Universities\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"border-b border-gray-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"text-left py-3 px-4 font-semibold text-gray-700\",\n                    children: \"University ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"text-left py-3 px-4 font-semibold text-gray-700\",\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"text-left py-3 px-4 font-semibold text-gray-700\",\n                    children: \"Contact\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"text-left py-3 px-4 font-semibold text-gray-700\",\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: universities.slice(0, 5).map(university => {\n                  var _university$name, _university$contactIn;\n                  return /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: \"border-b border-gray-100 hover:bg-gray-50\",\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"py-3 px-4\",\n                      children: university.universityId\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 384,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"py-3 px-4\",\n                      children: ((_university$name = university.name) === null || _university$name === void 0 ? void 0 : _university$name.en) || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"py-3 px-4\",\n                      children: ((_university$contactIn = university.contactInfo) === null || _university$contactIn === void 0 ? void 0 : _university$contactIn.email) || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 386,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"py-3 px-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => navigate('/superadmin/universities'),\n                        className: \"text-blue-600 hover:text-blue-800 font-medium\",\n                        children: \"View Details\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 388,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 387,\n                      columnNumber: 23\n                    }, this)]\n                  }, university.universityId, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 21\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this), showSuccessModal && /*#__PURE__*/_jsxDEV(SuccessModal, {\n      title: successTitle,\n      message: successMessage,\n      onClose: () => setShowSuccessModal(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 251,\n    columnNumber: 5\n  }, this);\n};\n_s(SuperAdminDashboard, \"yuygs7g74wwMoKmkryP6iLzH6zo=\", false, function () {\n  return [useNavigate, useAuth, useLocation];\n});\n_c = SuperAdminDashboard;\nexport default SuperAdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"SuperAdminDashboard\");", "map": {"version": 3, "names": ["useState", "useEffect", "useNavigate", "useLocation", "axios", "motion", "FaUniversity", "FaUserPlus", "FaNewspaper", "FaUsers", "<PERSON><PERSON><PERSON>", "Loader", "SuccessModal", "useAuth", "SuperAdminSidebar", "jsxDEV", "_jsxDEV", "SuperAdminDashboard", "_s", "_analytics$recentActi", "sidebarOpen", "setSidebarOpen", "showUniversityModal", "setShowUniversityModal", "showAccountModal", "setShowAccountModal", "showNewsModal", "setShowNewsModal", "showSuccessModal", "setShowSuccessModal", "successMessage", "setSuccessMessage", "successTitle", "setSuccessTitle", "universities", "setUniversities", "analytics", "setAnalytics", "totalUniversities", "totalAccounts", "recentActivity", "loading", "setLoading", "error", "setError", "navigate", "user", "token", "location", "editUniversity", "state", "universityForm", "setUniversityForm", "universityId", "name", "en", "ar", "address", "contactInfo", "phone", "email", "website", "description", "establishedYear", "accreditation", "faculties", "programs", "studentCapacity", "tuitionFees", "local", "international", "admissionRequirements", "campusSize", "numberOfFaculty", "researchCenters", "partnerships", "rankings", "national", "facilities", "scholarships", "languages", "semesterSystem", "academicCalendar", "fallStart", "fallEnd", "springStart", "springEnd", "summerStart", "summerEnd", "holidays", "accountForm", "setAccountForm", "password", "role", "studentId", "newsForm", "setNewsForm", "title", "content", "category", "priority", "targetAudience", "publishDate", "Date", "toISOString", "split", "expiryDate", "isActive", "fetchData", "config", "headers", "Authorization", "universitiesRes", "analyticsRes", "Promise", "all", "get", "process", "env", "REACT_APP_API_URL", "data", "err", "_err$response", "_err$response2", "_err$response3", "_err$response3$data", "_err$response4", "console", "response", "message", "errorMessage", "status", "handleUniversitySubmit", "e", "preventDefault", "trim", "put", "map", "u", "post", "_err$response5", "_err$response6", "_err$response6$data", "handleAccountSubmit", "_err$response7", "_err$response7$data", "handleNewsSubmit", "_err$response8", "_err$response8$data", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "isOpen", "setIsOpen", "div", "whileHover", "scale", "onClick", "length", "slice", "university", "_university$name", "_university$contactIn", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/superadmin/Dashboard.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport axios from 'axios';\nimport { motion } from 'framer-motion';\nimport { FaUniversity, FaUserPlus, FaNewspaper, FaUsers } from 'react-icons/fa';\nimport Navbar from '../student/Navbar';\nimport Loader from '../components/Loader';\nimport SuccessModal from '../components/SuccessModal';\nimport { useAuth } from '../context/AuthContext';\nimport SuperAdminSidebar from './SuperAdminSidebar';\n\nconst SuperAdminDashboard = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [showUniversityModal, setShowUniversityModal] = useState(false);\n  const [showAccountModal, setShowAccountModal] = useState(false);\n  const [showNewsModal, setShowNewsModal] = useState(false);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [successTitle, setSuccessTitle] = useState('Success');\n  const [universities, setUniversities] = useState([]);\n  const [analytics, setAnalytics] = useState({ totalUniversities: 0, totalAccounts: 0, recentActivity: [] });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  const { user, token } = useAuth();\n  const location = useLocation();\n  const { editUniversity } = location.state || {};\n\n  // University form state\n  const [universityForm, setUniversityForm] = useState({\n    universityId: '',\n    name: { en: '', ar: '' },\n    address: { en: '', ar: '' },\n    contactInfo: { phone: '', email: '', website: '' },\n    description: { en: '', ar: '' },\n    establishedYear: '',\n    accreditation: { en: '', ar: '' },\n    faculties: [],\n    programs: [],\n    studentCapacity: '',\n    tuitionFees: { local: '', international: '' },\n    admissionRequirements: { en: '', ar: '' },\n    campusSize: '',\n    numberOfFaculty: '',\n    researchCenters: [],\n    partnerships: [],\n    rankings: { national: '', international: '' },\n    facilities: [],\n    scholarships: [],\n    languages: [],\n    semesterSystem: 'semester',\n    academicCalendar: {\n      fallStart: '',\n      fallEnd: '',\n      springStart: '',\n      springEnd: '',\n      summerStart: '',\n      summerEnd: ''\n    },\n    holidays: ['Friday', 'Sunday']\n  });\n\n  // Account form state\n  const [accountForm, setAccountForm] = useState({\n    email: '',\n    password: '',\n    name: '',\n    role: '',\n    studentId: '',\n    universityId: ''\n  });\n\n  // News form state\n  const [newsForm, setNewsForm] = useState({\n    title: { en: '', ar: '' },\n    content: { en: '', ar: '' },\n    category: '',\n    priority: 'medium',\n    targetAudience: 'all',\n    publishDate: new Date().toISOString().split('T')[0],\n    expiryDate: '',\n    isActive: true\n  });\n\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!user || !token) {\n        navigate('/login');\n        return;\n      }\n\n      try {\n        const config = { headers: { Authorization: `Bearer ${token}` } };\n        const [universitiesRes, analyticsRes] = await Promise.all([\n          axios.get(`${process.env.REACT_APP_API_URL}/api/universities`, config),\n          axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/superadmin`, config),\n        ]);\n\n        setUniversities(universitiesRes.data || []);\n        setAnalytics(analyticsRes.data || { totalUniversities: 0, totalAccounts: 0, recentActivity: [] });\n      } catch (err) {\n        console.error('Fetch error:', err.response?.data || err.message);\n        const errorMessage = err.response?.status === 401\n          ? 'Unauthorized. Please log in again.'\n          : err.response?.data?.message || 'Failed to load data';\n        setError(errorMessage);\n        if (err.response?.status === 401) {\n          navigate('/login');\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n\n    if (editUniversity) setShowUniversityModal(true);\n  }, [user, token, navigate, editUniversity]);\n\n  const handleUniversitySubmit = async (e) => {\n    e.preventDefault();\n    try {\n      if (!universityForm.universityId.trim()) {\n        setError('University ID is required');\n        return;\n      }\n\n      const config = { headers: { Authorization: `Bearer ${token}` } };\n      \n      if (editUniversity) {\n        await axios.put(`${process.env.REACT_APP_API_URL}/api/universities/${universityForm.universityId}`, universityForm, config);\n        setUniversities(universities.map(u => u.universityId === universityForm.universityId ? { ...u, ...universityForm } : u));\n        setSuccessTitle('University Updated');\n        setSuccessMessage('University has been updated successfully!');\n      } else {\n        const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/universities`, universityForm, config);\n        setUniversities([...universities, response.data]);\n        setSuccessTitle('University Added');\n        setSuccessMessage('University has been added successfully!');\n      }\n      \n      setShowSuccessModal(true);\n      setShowUniversityModal(false);\n      \n      // Reset form\n      setUniversityForm({\n        universityId: '',\n        name: { en: '', ar: '' },\n        address: { en: '', ar: '' },\n        contactInfo: { phone: '', email: '', website: '' },\n        description: { en: '', ar: '' },\n        establishedYear: '',\n        accreditation: { en: '', ar: '' },\n        faculties: [],\n        programs: [],\n        studentCapacity: '',\n        tuitionFees: { local: '', international: '' },\n        admissionRequirements: { en: '', ar: '' },\n        campusSize: '',\n        numberOfFaculty: '',\n        researchCenters: [],\n        partnerships: [],\n        rankings: { national: '', international: '' },\n        facilities: [],\n        scholarships: [],\n        languages: [],\n        semesterSystem: 'semester',\n        academicCalendar: {\n          fallStart: '',\n          fallEnd: '',\n          springStart: '',\n          springEnd: '',\n          summerStart: '',\n          summerEnd: ''\n        },\n        holidays: ['Friday', 'Sunday']\n      });\n\n      // Refresh analytics\n      const analyticsRes = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/superadmin`, config);\n      setAnalytics(analyticsRes.data || { totalUniversities: 0, totalAccounts: 0, recentActivity: [] });\n\n      navigate('/superadmin/dashboard', { state: {} });\n    } catch (err) {\n      console.error('University submission error:', err);\n      const errorMessage = err.response?.status === 401\n        ? 'Unauthorized. Please log in again.'\n        : err.response?.data?.message || 'Failed to save university';\n      setError(errorMessage);\n    }\n  };\n\n  const handleAccountSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      const config = { headers: { Authorization: `Bearer ${token}` } };\n      await axios.post(`${process.env.REACT_APP_API_URL}/api/accounts`, accountForm, config);\n      \n      setShowAccountModal(false);\n      setAccountForm({\n        email: '',\n        password: '',\n        name: '',\n        role: '',\n        studentId: '',\n        universityId: ''\n      });\n      \n      setSuccessTitle('Account Created');\n      setSuccessMessage('Account has been created successfully!');\n      setShowSuccessModal(true);\n\n      // Refresh analytics\n      const analyticsRes = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/superadmin`, config);\n      setAnalytics(analyticsRes.data || { totalUniversities: 0, totalAccounts: 0, recentActivity: [] });\n    } catch (err) {\n      console.error('Account creation error:', err);\n      setError(err.response?.data?.message || 'Failed to add account');\n    }\n  };\n\n  const handleNewsSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      const config = { headers: { Authorization: `Bearer ${token}` } };\n      await axios.post(`${process.env.REACT_APP_API_URL}/api/news`, newsForm, config);\n      \n      setShowNewsModal(false);\n      setNewsForm({\n        title: { en: '', ar: '' },\n        content: { en: '', ar: '' },\n        category: '',\n        priority: 'medium',\n        targetAudience: 'all',\n        publishDate: new Date().toISOString().split('T')[0],\n        expiryDate: '',\n        isActive: true\n      });\n      \n      setSuccessTitle('News Published');\n      setSuccessMessage('News has been published successfully!');\n      setShowSuccessModal(true);\n    } catch (err) {\n      console.error('News creation error:', err);\n      setError(err.response?.data?.message || 'Failed to publish news');\n    }\n  };\n\n  if (loading) return <Loader />;\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50\">\n      <Navbar />\n      <SuperAdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n      \n      <div className=\"lg:ml-64 pt-16\">\n        <div className=\"p-6\">\n          <div className=\"mb-8\">\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Superadmin Dashboard</h1>\n            <p className=\"text-gray-600\">Manage universities, accounts, and system-wide settings</p>\n          </div>\n\n          {error && (\n            <div className=\"mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\">\n              {error}\n            </div>\n          )}\n\n          {/* Quick Actions */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"bg-white p-6 rounded-xl shadow-lg border border-gray-100 cursor-pointer\"\n              onClick={() => setShowUniversityModal(true)}\n            >\n              <div className=\"flex items-center\">\n                <div className=\"p-3 bg-blue-100 rounded-lg\">\n                  <FaUniversity className=\"h-6 w-6 text-blue-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <h3 className=\"text-lg font-semibold text-gray-900\">Add University</h3>\n                  <p className=\"text-gray-600\">Create new university</p>\n                </div>\n              </div>\n            </motion.div>\n\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"bg-white p-6 rounded-xl shadow-lg border border-gray-100 cursor-pointer\"\n              onClick={() => setShowAccountModal(true)}\n            >\n              <div className=\"flex items-center\">\n                <div className=\"p-3 bg-green-100 rounded-lg\">\n                  <FaUserPlus className=\"h-6 w-6 text-green-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <h3 className=\"text-lg font-semibold text-gray-900\">Add Account</h3>\n                  <p className=\"text-gray-600\">Create user account</p>\n                </div>\n              </div>\n            </motion.div>\n\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"bg-white p-6 rounded-xl shadow-lg border border-gray-100 cursor-pointer\"\n              onClick={() => setShowNewsModal(true)}\n            >\n              <div className=\"flex items-center\">\n                <div className=\"p-3 bg-purple-100 rounded-lg\">\n                  <FaNewspaper className=\"h-6 w-6 text-purple-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <h3 className=\"text-lg font-semibold text-gray-900\">Publish News</h3>\n                  <p className=\"text-gray-600\">Create news article</p>\n                </div>\n              </div>\n            </motion.div>\n\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"bg-white p-6 rounded-xl shadow-lg border border-gray-100 cursor-pointer\"\n              onClick={() => navigate('/superadmin/accounts')}\n            >\n              <div className=\"flex items-center\">\n                <div className=\"p-3 bg-orange-100 rounded-lg\">\n                  <FaUsers className=\"h-6 w-6 text-orange-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <h3 className=\"text-lg font-semibold text-gray-900\">Manage Accounts</h3>\n                  <p className=\"text-gray-600\">View all accounts</p>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Analytics Cards */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n            <div className=\"bg-white p-6 rounded-xl shadow-lg border border-gray-100\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-gray-600 text-sm\">Total Universities</p>\n                  <p className=\"text-3xl font-bold text-blue-600\">{analytics.totalUniversities || 0}</p>\n                </div>\n                <FaUniversity className=\"h-8 w-8 text-blue-600\" />\n              </div>\n            </div>\n\n            <div className=\"bg-white p-6 rounded-xl shadow-lg border border-gray-100\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-gray-600 text-sm\">Total Accounts</p>\n                  <p className=\"text-3xl font-bold text-green-600\">{analytics.totalAccounts || 0}</p>\n                </div>\n                <FaUsers className=\"h-8 w-8 text-green-600\" />\n              </div>\n            </div>\n\n            <div className=\"bg-white p-6 rounded-xl shadow-lg border border-gray-100\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-gray-600 text-sm\">Recent Activity</p>\n                  <p className=\"text-3xl font-bold text-purple-600\">{analytics.recentActivity?.length || 0}</p>\n                </div>\n                <FaNewspaper className=\"h-8 w-8 text-purple-600\" />\n              </div>\n            </div>\n          </div>\n\n          {/* Recent Universities */}\n          <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Recent Universities</h2>\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead>\n                  <tr className=\"border-b border-gray-200\">\n                    <th className=\"text-left py-3 px-4 font-semibold text-gray-700\">University ID</th>\n                    <th className=\"text-left py-3 px-4 font-semibold text-gray-700\">Name</th>\n                    <th className=\"text-left py-3 px-4 font-semibold text-gray-700\">Contact</th>\n                    <th className=\"text-left py-3 px-4 font-semibold text-gray-700\">Actions</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {universities.slice(0, 5).map((university) => (\n                    <tr key={university.universityId} className=\"border-b border-gray-100 hover:bg-gray-50\">\n                      <td className=\"py-3 px-4\">{university.universityId}</td>\n                      <td className=\"py-3 px-4\">{university.name?.en || 'N/A'}</td>\n                      <td className=\"py-3 px-4\">{university.contactInfo?.email || 'N/A'}</td>\n                      <td className=\"py-3 px-4\">\n                        <button\n                          onClick={() => navigate('/superadmin/universities')}\n                          className=\"text-blue-600 hover:text-blue-800 font-medium\"\n                        >\n                          View Details\n                        </button>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Success Modal */}\n      {showSuccessModal && (\n        <SuccessModal\n          title={successTitle}\n          message={successMessage}\n          onClose={() => setShowSuccessModal(false)}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default SuperAdminDashboard;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,YAAY,EAAEC,UAAU,EAAEC,WAAW,EAAEC,OAAO,QAAQ,gBAAgB;AAC/E,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAChC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACwB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8B,cAAc,EAAEC,iBAAiB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,SAAS,CAAC;EAC3D,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC;IAAEsC,iBAAiB,EAAE,CAAC;IAAEC,aAAa,EAAE,CAAC;IAAEC,cAAc,EAAE;EAAG,CAAC,CAAC;EAC1G,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2C,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM6C,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4C,IAAI;IAAEC;EAAM,CAAC,GAAGlC,OAAO,CAAC,CAAC;EACjC,MAAMmC,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE8C;EAAe,CAAC,GAAGD,QAAQ,CAACE,KAAK,IAAI,CAAC,CAAC;;EAE/C;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAC;IACnDqD,YAAY,EAAE,EAAE;IAChBC,IAAI,EAAE;MAAEC,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC;IACxBC,OAAO,EAAE;MAAEF,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC;IAC3BE,WAAW,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAC;IAClDC,WAAW,EAAE;MAAEP,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC;IAC/BO,eAAe,EAAE,EAAE;IACnBC,aAAa,EAAE;MAAET,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC;IACjCS,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC;IAC7CC,qBAAqB,EAAE;MAAEhB,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC;IACzCgB,UAAU,EAAE,EAAE;IACdC,eAAe,EAAE,EAAE;IACnBC,eAAe,EAAE,EAAE;IACnBC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEP,aAAa,EAAE;IAAG,CAAC;IAC7CQ,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,EAAE;IACbC,cAAc,EAAE,UAAU;IAC1BC,gBAAgB,EAAE;MAChBC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,EAAE;MACXC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE;IACb,CAAC;IACDC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ;EAC/B,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3F,QAAQ,CAAC;IAC7C4D,KAAK,EAAE,EAAE;IACTgC,QAAQ,EAAE,EAAE;IACZtC,IAAI,EAAE,EAAE;IACRuC,IAAI,EAAE,EAAE;IACRC,SAAS,EAAE,EAAE;IACbzC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAGhG,QAAQ,CAAC;IACvCiG,KAAK,EAAE;MAAE1C,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC;IACzB0C,OAAO,EAAE;MAAE3C,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC;IAC3B2C,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,QAAQ;IAClBC,cAAc,EAAE,KAAK;IACrBC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACnDC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF1G,SAAS,CAAC,MAAM;IACd,MAAM2G,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI,CAAC9D,IAAI,IAAI,CAACC,KAAK,EAAE;QACnBF,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MAEA,IAAI;QACF,MAAMgE,MAAM,GAAG;UAAEC,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUhE,KAAK;UAAG;QAAE,CAAC;QAChE,MAAM,CAACiE,eAAe,EAAEC,YAAY,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACxD/G,KAAK,CAACgH,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,mBAAmB,EAAEV,MAAM,CAAC,EACtEzG,KAAK,CAACgH,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,4BAA4B,EAAEV,MAAM,CAAC,CAChF,CAAC;QAEF1E,eAAe,CAAC6E,eAAe,CAACQ,IAAI,IAAI,EAAE,CAAC;QAC3CnF,YAAY,CAAC4E,YAAY,CAACO,IAAI,IAAI;UAAElF,iBAAiB,EAAE,CAAC;UAAEC,aAAa,EAAE,CAAC;UAAEC,cAAc,EAAE;QAAG,CAAC,CAAC;MACnG,CAAC,CAAC,OAAOiF,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA,EAAAC,cAAA;QACZC,OAAO,CAACpF,KAAK,CAAC,cAAc,EAAE,EAAA+E,aAAA,GAAAD,GAAG,CAACO,QAAQ,cAAAN,aAAA,uBAAZA,aAAA,CAAcF,IAAI,KAAIC,GAAG,CAACQ,OAAO,CAAC;QAChE,MAAMC,YAAY,GAAG,EAAAP,cAAA,GAAAF,GAAG,CAACO,QAAQ,cAAAL,cAAA,uBAAZA,cAAA,CAAcQ,MAAM,MAAK,GAAG,GAC7C,oCAAoC,GACpC,EAAAP,cAAA,GAAAH,GAAG,CAACO,QAAQ,cAAAJ,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcJ,IAAI,cAAAK,mBAAA,uBAAlBA,mBAAA,CAAoBI,OAAO,KAAI,qBAAqB;QACxDrF,QAAQ,CAACsF,YAAY,CAAC;QACtB,IAAI,EAAAJ,cAAA,GAAAL,GAAG,CAACO,QAAQ,cAAAF,cAAA,uBAAZA,cAAA,CAAcK,MAAM,MAAK,GAAG,EAAE;UAChCtF,QAAQ,CAAC,QAAQ,CAAC;QACpB;MACF,CAAC,SAAS;QACRH,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDkE,SAAS,CAAC,CAAC;IAEX,IAAI3D,cAAc,EAAE1B,sBAAsB,CAAC,IAAI,CAAC;EAClD,CAAC,EAAE,CAACuB,IAAI,EAAEC,KAAK,EAAEF,QAAQ,EAAEI,cAAc,CAAC,CAAC;EAE3C,MAAMmF,sBAAsB,GAAG,MAAOC,CAAC,IAAK;IAC1CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,IAAI,CAACnF,cAAc,CAACE,YAAY,CAACkF,IAAI,CAAC,CAAC,EAAE;QACvC3F,QAAQ,CAAC,2BAA2B,CAAC;QACrC;MACF;MAEA,MAAMiE,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUhE,KAAK;QAAG;MAAE,CAAC;MAEhE,IAAIE,cAAc,EAAE;QAClB,MAAM7C,KAAK,CAACoI,GAAG,CAAC,GAAGnB,OAAO,CAACC,GAAG,CAACC,iBAAiB,qBAAqBpE,cAAc,CAACE,YAAY,EAAE,EAAEF,cAAc,EAAE0D,MAAM,CAAC;QAC3H1E,eAAe,CAACD,YAAY,CAACuG,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACrF,YAAY,KAAKF,cAAc,CAACE,YAAY,GAAG;UAAE,GAAGqF,CAAC;UAAE,GAAGvF;QAAe,CAAC,GAAGuF,CAAC,CAAC,CAAC;QACxHzG,eAAe,CAAC,oBAAoB,CAAC;QACrCF,iBAAiB,CAAC,2CAA2C,CAAC;MAChE,CAAC,MAAM;QACL,MAAMiG,QAAQ,GAAG,MAAM5H,KAAK,CAACuI,IAAI,CAAC,GAAGtB,OAAO,CAACC,GAAG,CAACC,iBAAiB,mBAAmB,EAAEpE,cAAc,EAAE0D,MAAM,CAAC;QAC9G1E,eAAe,CAAC,CAAC,GAAGD,YAAY,EAAE8F,QAAQ,CAACR,IAAI,CAAC,CAAC;QACjDvF,eAAe,CAAC,kBAAkB,CAAC;QACnCF,iBAAiB,CAAC,yCAAyC,CAAC;MAC9D;MAEAF,mBAAmB,CAAC,IAAI,CAAC;MACzBN,sBAAsB,CAAC,KAAK,CAAC;;MAE7B;MACA6B,iBAAiB,CAAC;QAChBC,YAAY,EAAE,EAAE;QAChBC,IAAI,EAAE;UAAEC,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QACxBC,OAAO,EAAE;UAAEF,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QAC3BE,WAAW,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAG,CAAC;QAClDC,WAAW,EAAE;UAAEP,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QAC/BO,eAAe,EAAE,EAAE;QACnBC,aAAa,EAAE;UAAET,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QACjCS,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZC,eAAe,EAAE,EAAE;QACnBC,WAAW,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,aAAa,EAAE;QAAG,CAAC;QAC7CC,qBAAqB,EAAE;UAAEhB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QACzCgB,UAAU,EAAE,EAAE;QACdC,eAAe,EAAE,EAAE;QACnBC,eAAe,EAAE,EAAE;QACnBC,YAAY,EAAE,EAAE;QAChBC,QAAQ,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEP,aAAa,EAAE;QAAG,CAAC;QAC7CQ,UAAU,EAAE,EAAE;QACdC,YAAY,EAAE,EAAE;QAChBC,SAAS,EAAE,EAAE;QACbC,cAAc,EAAE,UAAU;QAC1BC,gBAAgB,EAAE;UAChBC,SAAS,EAAE,EAAE;UACbC,OAAO,EAAE,EAAE;UACXC,WAAW,EAAE,EAAE;UACfC,SAAS,EAAE,EAAE;UACbC,WAAW,EAAE,EAAE;UACfC,SAAS,EAAE;QACb,CAAC;QACDC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ;MAC/B,CAAC,CAAC;;MAEF;MACA,MAAMwB,YAAY,GAAG,MAAM7G,KAAK,CAACgH,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,4BAA4B,EAAEV,MAAM,CAAC;MAC1GxE,YAAY,CAAC4E,YAAY,CAACO,IAAI,IAAI;QAAElF,iBAAiB,EAAE,CAAC;QAAEC,aAAa,EAAE,CAAC;QAAEC,cAAc,EAAE;MAAG,CAAC,CAAC;MAEjGK,QAAQ,CAAC,uBAAuB,EAAE;QAAEK,KAAK,EAAE,CAAC;MAAE,CAAC,CAAC;IAClD,CAAC,CAAC,OAAOuE,GAAG,EAAE;MAAA,IAAAmB,cAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZf,OAAO,CAACpF,KAAK,CAAC,8BAA8B,EAAE8E,GAAG,CAAC;MAClD,MAAMS,YAAY,GAAG,EAAAU,cAAA,GAAAnB,GAAG,CAACO,QAAQ,cAAAY,cAAA,uBAAZA,cAAA,CAAcT,MAAM,MAAK,GAAG,GAC7C,oCAAoC,GACpC,EAAAU,cAAA,GAAApB,GAAG,CAACO,QAAQ,cAAAa,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcrB,IAAI,cAAAsB,mBAAA,uBAAlBA,mBAAA,CAAoBb,OAAO,KAAI,2BAA2B;MAC9DrF,QAAQ,CAACsF,YAAY,CAAC;IACxB;EACF,CAAC;EAED,MAAMa,mBAAmB,GAAG,MAAOV,CAAC,IAAK;IACvCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMzB,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUhE,KAAK;QAAG;MAAE,CAAC;MAChE,MAAM3C,KAAK,CAACuI,IAAI,CAAC,GAAGtB,OAAO,CAACC,GAAG,CAACC,iBAAiB,eAAe,EAAE7B,WAAW,EAAEmB,MAAM,CAAC;MAEtFpF,mBAAmB,CAAC,KAAK,CAAC;MAC1BkE,cAAc,CAAC;QACb/B,KAAK,EAAE,EAAE;QACTgC,QAAQ,EAAE,EAAE;QACZtC,IAAI,EAAE,EAAE;QACRuC,IAAI,EAAE,EAAE;QACRC,SAAS,EAAE,EAAE;QACbzC,YAAY,EAAE;MAChB,CAAC,CAAC;MAEFpB,eAAe,CAAC,iBAAiB,CAAC;MAClCF,iBAAiB,CAAC,wCAAwC,CAAC;MAC3DF,mBAAmB,CAAC,IAAI,CAAC;;MAEzB;MACA,MAAMoF,YAAY,GAAG,MAAM7G,KAAK,CAACgH,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,4BAA4B,EAAEV,MAAM,CAAC;MAC1GxE,YAAY,CAAC4E,YAAY,CAACO,IAAI,IAAI;QAAElF,iBAAiB,EAAE,CAAC;QAAEC,aAAa,EAAE,CAAC;QAAEC,cAAc,EAAE;MAAG,CAAC,CAAC;IACnG,CAAC,CAAC,OAAOiF,GAAG,EAAE;MAAA,IAAAuB,cAAA,EAAAC,mBAAA;MACZlB,OAAO,CAACpF,KAAK,CAAC,yBAAyB,EAAE8E,GAAG,CAAC;MAC7C7E,QAAQ,CAAC,EAAAoG,cAAA,GAAAvB,GAAG,CAACO,QAAQ,cAAAgB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcxB,IAAI,cAAAyB,mBAAA,uBAAlBA,mBAAA,CAAoBhB,OAAO,KAAI,uBAAuB,CAAC;IAClE;EACF,CAAC;EAED,MAAMiB,gBAAgB,GAAG,MAAOb,CAAC,IAAK;IACpCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMzB,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUhE,KAAK;QAAG;MAAE,CAAC;MAChE,MAAM3C,KAAK,CAACuI,IAAI,CAAC,GAAGtB,OAAO,CAACC,GAAG,CAACC,iBAAiB,WAAW,EAAExB,QAAQ,EAAEc,MAAM,CAAC;MAE/ElF,gBAAgB,CAAC,KAAK,CAAC;MACvBqE,WAAW,CAAC;QACVC,KAAK,EAAE;UAAE1C,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QACzB0C,OAAO,EAAE;UAAE3C,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QAC3B2C,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,QAAQ;QAClBC,cAAc,EAAE,KAAK;QACrBC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnDC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF1E,eAAe,CAAC,gBAAgB,CAAC;MACjCF,iBAAiB,CAAC,uCAAuC,CAAC;MAC1DF,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAO4F,GAAG,EAAE;MAAA,IAAA0B,cAAA,EAAAC,mBAAA;MACZrB,OAAO,CAACpF,KAAK,CAAC,sBAAsB,EAAE8E,GAAG,CAAC;MAC1C7E,QAAQ,CAAC,EAAAuG,cAAA,GAAA1B,GAAG,CAACO,QAAQ,cAAAmB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc3B,IAAI,cAAA4B,mBAAA,uBAAlBA,mBAAA,CAAoBnB,OAAO,KAAI,wBAAwB,CAAC;IACnE;EACF,CAAC;EAED,IAAIxF,OAAO,EAAE,oBAAOzB,OAAA,CAACL,MAAM;IAAA0I,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAE9B,oBACExI,OAAA;IAAKyI,SAAS,EAAC,kEAAkE;IAAAC,QAAA,gBAC/E1I,OAAA,CAACN,MAAM;MAAA2I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVxI,OAAA,CAACF,iBAAiB;MAAC6I,MAAM,EAAEvI,WAAY;MAACwI,SAAS,EAAEvI;IAAe;MAAAgI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAErExI,OAAA;MAAKyI,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7B1I,OAAA;QAAKyI,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAClB1I,OAAA;UAAKyI,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB1I,OAAA;YAAIyI,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAoB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/ExI,OAAA;YAAGyI,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAuD;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC,EAEL7G,KAAK,iBACJ3B,OAAA;UAAKyI,SAAS,EAAC,wEAAwE;UAAAC,QAAA,EACpF/G;QAAK;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDxI,OAAA;UAAKyI,SAAS,EAAC,2DAA2D;UAAAC,QAAA,gBACxE1I,OAAA,CAACX,MAAM,CAACwJ,GAAG;YACTC,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BN,SAAS,EAAC,yEAAyE;YACnFO,OAAO,EAAEA,CAAA,KAAMzI,sBAAsB,CAAC,IAAI,CAAE;YAAAmI,QAAA,eAE5C1I,OAAA;cAAKyI,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC1I,OAAA;gBAAKyI,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,eACzC1I,OAAA,CAACV,YAAY;kBAACmJ,SAAS,EAAC;gBAAuB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACNxI,OAAA;gBAAKyI,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB1I,OAAA;kBAAIyI,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAc;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvExI,OAAA;kBAAGyI,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbxI,OAAA,CAACX,MAAM,CAACwJ,GAAG;YACTC,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BN,SAAS,EAAC,yEAAyE;YACnFO,OAAO,EAAEA,CAAA,KAAMvI,mBAAmB,CAAC,IAAI,CAAE;YAAAiI,QAAA,eAEzC1I,OAAA;cAAKyI,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC1I,OAAA;gBAAKyI,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eAC1C1I,OAAA,CAACT,UAAU;kBAACkJ,SAAS,EAAC;gBAAwB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACNxI,OAAA;gBAAKyI,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB1I,OAAA;kBAAIyI,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAW;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpExI,OAAA;kBAAGyI,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbxI,OAAA,CAACX,MAAM,CAACwJ,GAAG;YACTC,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BN,SAAS,EAAC,yEAAyE;YACnFO,OAAO,EAAEA,CAAA,KAAMrI,gBAAgB,CAAC,IAAI,CAAE;YAAA+H,QAAA,eAEtC1I,OAAA;cAAKyI,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC1I,OAAA;gBAAKyI,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,eAC3C1I,OAAA,CAACR,WAAW;kBAACiJ,SAAS,EAAC;gBAAyB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACNxI,OAAA;gBAAKyI,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB1I,OAAA;kBAAIyI,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrExI,OAAA;kBAAGyI,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbxI,OAAA,CAACX,MAAM,CAACwJ,GAAG;YACTC,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BN,SAAS,EAAC,yEAAyE;YACnFO,OAAO,EAAEA,CAAA,KAAMnH,QAAQ,CAAC,sBAAsB,CAAE;YAAA6G,QAAA,eAEhD1I,OAAA;cAAKyI,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC1I,OAAA;gBAAKyI,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,eAC3C1I,OAAA,CAACP,OAAO;kBAACgJ,SAAS,EAAC;gBAAyB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACNxI,OAAA;gBAAKyI,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB1I,OAAA;kBAAIyI,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAe;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxExI,OAAA;kBAAGyI,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNxI,OAAA;UAAKyI,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBACzD1I,OAAA;YAAKyI,SAAS,EAAC,0DAA0D;YAAAC,QAAA,eACvE1I,OAAA;cAAKyI,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD1I,OAAA;gBAAA0I,QAAA,gBACE1I,OAAA;kBAAGyI,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC3DxI,OAAA;kBAAGyI,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAEtH,SAAS,CAACE,iBAAiB,IAAI;gBAAC;kBAAA+G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC,eACNxI,OAAA,CAACV,YAAY;gBAACmJ,SAAS,EAAC;cAAuB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxI,OAAA;YAAKyI,SAAS,EAAC,0DAA0D;YAAAC,QAAA,eACvE1I,OAAA;cAAKyI,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD1I,OAAA;gBAAA0I,QAAA,gBACE1I,OAAA;kBAAGyI,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAc;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACvDxI,OAAA;kBAAGyI,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEtH,SAAS,CAACG,aAAa,IAAI;gBAAC;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eACNxI,OAAA,CAACP,OAAO;gBAACgJ,SAAS,EAAC;cAAwB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxI,OAAA;YAAKyI,SAAS,EAAC,0DAA0D;YAAAC,QAAA,eACvE1I,OAAA;cAAKyI,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD1I,OAAA;gBAAA0I,QAAA,gBACE1I,OAAA;kBAAGyI,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAe;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACxDxI,OAAA;kBAAGyI,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAE,EAAAvI,qBAAA,GAAAiB,SAAS,CAACI,cAAc,cAAArB,qBAAA,uBAAxBA,qBAAA,CAA0B8I,MAAM,KAAI;gBAAC;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1F,CAAC,eACNxI,OAAA,CAACR,WAAW;gBAACiJ,SAAS,EAAC;cAAyB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxI,OAAA;UAAKyI,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACvE1I,OAAA;YAAIyI,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAmB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjFxI,OAAA;YAAKyI,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9B1I,OAAA;cAAOyI,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACvB1I,OAAA;gBAAA0I,QAAA,eACE1I,OAAA;kBAAIyI,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACtC1I,OAAA;oBAAIyI,SAAS,EAAC,iDAAiD;oBAAAC,QAAA,EAAC;kBAAa;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClFxI,OAAA;oBAAIyI,SAAS,EAAC,iDAAiD;oBAAAC,QAAA,EAAC;kBAAI;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzExI,OAAA;oBAAIyI,SAAS,EAAC,iDAAiD;oBAAAC,QAAA,EAAC;kBAAO;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5ExI,OAAA;oBAAIyI,SAAS,EAAC,iDAAiD;oBAAAC,QAAA,EAAC;kBAAO;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRxI,OAAA;gBAAA0I,QAAA,EACGxH,YAAY,CAACgI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACzB,GAAG,CAAE0B,UAAU;kBAAA,IAAAC,gBAAA,EAAAC,qBAAA;kBAAA,oBACvCrJ,OAAA;oBAAkCyI,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,gBACrF1I,OAAA;sBAAIyI,SAAS,EAAC,WAAW;sBAAAC,QAAA,EAAES,UAAU,CAAC9G;oBAAY;sBAAAgG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxDxI,OAAA;sBAAIyI,SAAS,EAAC,WAAW;sBAAAC,QAAA,EAAE,EAAAU,gBAAA,GAAAD,UAAU,CAAC7G,IAAI,cAAA8G,gBAAA,uBAAfA,gBAAA,CAAiB7G,EAAE,KAAI;oBAAK;sBAAA8F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC7DxI,OAAA;sBAAIyI,SAAS,EAAC,WAAW;sBAAAC,QAAA,EAAE,EAAAW,qBAAA,GAAAF,UAAU,CAACzG,WAAW,cAAA2G,qBAAA,uBAAtBA,qBAAA,CAAwBzG,KAAK,KAAI;oBAAK;sBAAAyF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvExI,OAAA;sBAAIyI,SAAS,EAAC,WAAW;sBAAAC,QAAA,eACvB1I,OAAA;wBACEgJ,OAAO,EAAEA,CAAA,KAAMnH,QAAQ,CAAC,0BAA0B,CAAE;wBACpD4G,SAAS,EAAC,+CAA+C;wBAAAC,QAAA,EAC1D;sBAED;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC;kBAAA,GAXEW,UAAU,CAAC9G,YAAY;oBAAAgG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAY5B,CAAC;gBAAA,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL5H,gBAAgB,iBACfZ,OAAA,CAACJ,YAAY;MACXqF,KAAK,EAAEjE,YAAa;MACpBiG,OAAO,EAAEnG,cAAe;MACxBwI,OAAO,EAAEA,CAAA,KAAMzI,mBAAmB,CAAC,KAAK;IAAE;MAAAwH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtI,EAAA,CAlZID,mBAAmB;EAAA,QAYNf,WAAW,EACJW,OAAO,EACdV,WAAW;AAAA;AAAAoK,EAAA,GAdxBtJ,mBAAmB;AAoZzB,eAAeA,mBAAmB;AAAC,IAAAsJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}