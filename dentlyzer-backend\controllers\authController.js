const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const Joi = require('joi');
const { OAuth2Client } = require('google-auth-library');
const config = require('../config/config');
const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');
const { StudentHelpers } = require('../models/firebase/Student');
const { SupervisorHelpers } = require('../models/firebase/Supervisor');
const { AdminHelpers } = require('../models/firebase/Admin');
const superadminEmails = require('../config/superadmins');
const { config: emailConfig } = require('../config/superadmins');

// Initialize Google OAuth client
const googleClient = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);


const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
});

const googleLoginSchema = Joi.object({
  token: Joi.string().required(),
});

const login = async (req, res) => {
  const { error } = loginSchema.validate(req.body);
  if (error) {
    return res.status(400).json({ message: error.details[0].message });
  }

  const { email, password } = req.body;

  try {
    const userTypes = [
      { collection: COLLECTIONS.STUDENTS, role: 'student' },
      { collection: COLLECTIONS.SUPERVISORS, role: 'supervisor' },
      { collection: COLLECTIONS.ADMINS, role: 'admin' },
      { collection: COLLECTIONS.ASSISTANTS, role: 'assistant' },
    ];

    let foundUser = null;
    let role = '';

    // Check superadmin collection FIRST (highest priority)
    const superadmin = await FirestoreHelpers.findOne(COLLECTIONS.CONFIGS, { field: 'email', operator: '==', value: email });
    console.log(`Checking superadmin for email ${email}: ${superadmin ? 'Found' : 'Not found'}`);
    if (superadmin && superadmin.role === 'superadmin') {
      const isMatch = await bcrypt.compare(password, superadmin.password);
      console.log(`Password match for superadmin: ${isMatch}`);
      if (isMatch) {
        foundUser = superadmin;
        role = 'superadmin';
      }
    }

    // If not superadmin, check other collections
    if (!foundUser) {
      for (const { collection, role: roleName } of userTypes) {
        const user = await FirestoreHelpers.findOne(collection, { field: 'email', operator: '==', value: email });
        console.log(`Checking ${roleName} for email ${email}: ${user ? 'Found' : 'Not found'}`);
        if (user) {
          const isMatch = await bcrypt.compare(password, user.password);
          console.log(`Password match for ${roleName}: ${isMatch}`);
          if (isMatch) {
            foundUser = user;
            role = roleName;
            break;
          }
        }
      }
    }

    if (!foundUser) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    console.log('Signing JWT for:', { id: foundUser.id, role }); // Debug log
    const token = jwt.sign(
      { id: foundUser.id, role },
      config.JWT_SECRET,
      { expiresIn: config.JWT_ACCESS_EXPIRATION }
    );

    // Customize the user object based on role
    let userResponse = {
      id: foundUser.id,
      email: foundUser.email,
      name: foundUser.name,
      role,
      university: foundUser.university || '',
    };

    // Add role-specific fields
    if (role === 'student') {
      userResponse.studentId = foundUser.studentId || foundUser.id;
    } else if (role === 'assistant') {
      userResponse.dentistId = foundUser.dentistId;
      // Include affiliation information if available
      if (foundUser.affiliation) {
        userResponse.affiliation = foundUser.affiliation;
      }
    }

    // Log successful login
    try {
      await FirestoreHelpers.create(COLLECTIONS.ACTIVITY_LOGS, {
        userId: foundUser.id,
        userName: foundUser.name,
        userRole: role,
        action: 'User logged in',
        details: `Email: ${email}`,
        ipAddress: req.ip || req.connection.remoteAddress || 'Unknown',
        timestamp: new Date()
      });
    } catch (logError) {
      console.error('Error logging login activity:', logError);
      // Don't fail login if logging fails
    }

    res.json({
      token,
      role,
      user: userResponse,
    });
  } catch (err) {
    console.error('Login error:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Google OAuth login
const googleLogin = async (req, res) => {
  const { error } = googleLoginSchema.validate(req.body);
  if (error) {
    return res.status(400).json({ message: error.details[0].message });
  }

  const { token } = req.body;

  try {
    // Verify the Google token
    const ticket = await googleClient.verifyIdToken({
      idToken: token,
      audience: process.env.GOOGLE_CLIENT_ID,
    });

    const payload = ticket.getPayload();
    const { email, name, picture, sub: googleId } = payload;

    console.log('Google login attempt for:', email);

    let foundUser = null;
    let role = '';

    // Check superadmin collection FIRST (highest priority)
    const superadmin = await FirestoreHelpers.findOne(COLLECTIONS.CONFIGS, { field: 'email', operator: '==', value: email });
    if (superadmin && superadmin.role === 'superadmin') {
      foundUser = superadmin;
      role = 'superadmin';
    }

    // If not superadmin, check other collections
    if (!foundUser) {
      const userTypes = [
        { collection: COLLECTIONS.STUDENTS, role: 'student' },
        { collection: COLLECTIONS.SUPERVISORS, role: 'supervisor' },
        { collection: COLLECTIONS.ADMINS, role: 'admin' },
        { collection: COLLECTIONS.ASSISTANTS, role: 'assistant' },
      ];

      // Check standard collections
      for (const { collection, role: roleName } of userTypes) {
        const user = await FirestoreHelpers.findOne(collection, { field: 'email', operator: '==', value: email });
        if (user) {
          foundUser = user;
          role = roleName;
          break;
        }
      }
    }

    // If user doesn't exist, check if they should be created automatically
    if (!foundUser) {
      let newUserData = null;
      let targetCollection = null;
      let newRole = null;

      // Check if email is authorized for automatic account creation
      if (superadminEmails.includes(email)) {
        // Create new superadmin account
        newUserData = {
          email,
          name,
          role: 'superadmin',
          googleId,
          picture,
          password: '', // No password for Google auth
          plainPassword: '',
          createdAt: new Date(),
          updatedAt: new Date()
        };
        targetCollection = COLLECTIONS.CONFIGS;
        newRole = 'superadmin';
        console.log('Creating new superadmin account for:', email);
      } else if (emailConfig?.authorizedEmails?.admins?.includes(email)) {
        // Create new admin account
        newUserData = {
          email,
          name,
          role: 'admin',
          googleId,
          picture,
          password: '',
          plainPassword: '',
          university: 'AIU', // Default university, can be changed later
          createdAt: new Date(),
          updatedAt: new Date()
        };
        targetCollection = COLLECTIONS.ADMINS;
        newRole = 'admin';
        console.log('Creating new admin account for:', email);
      } else if (emailConfig?.authorizedEmails?.supervisors?.includes(email)) {
        // Create new supervisor account
        newUserData = {
          email,
          name,
          role: 'supervisor',
          googleId,
          picture,
          password: '',
          plainPassword: '',
          university: 'AIU', // Default university, can be changed later
          createdAt: new Date(),
          updatedAt: new Date()
        };
        targetCollection = COLLECTIONS.SUPERVISORS;
        newRole = 'supervisor';
        console.log('Creating new supervisor account for:', email);
      } else if (emailConfig?.authorizedEmails?.students?.includes(email)) {
        // Create new student account
        newUserData = {
          email,
          name,
          role: 'student',
          googleId,
          picture,
          password: '',
          plainPassword: '',
          university: 'AIU', // Default university, can be changed later
          studentId: `STU${Date.now()}`, // Generate temporary student ID
          patients: [],
          reviews: [],
          appointments: [],
          createdAt: new Date(),
          updatedAt: new Date()
        };
        targetCollection = COLLECTIONS.STUDENTS;
        newRole = 'student';
        console.log('Creating new student account for:', email);
      } else if (emailConfig?.authorizedEmails?.assistants?.includes(email)) {
        // Create new assistant account
        newUserData = {
          email,
          name,
          role: 'assistant',
          googleId,
          picture,
          password: '',
          plainPassword: '',
          university: 'AIU', // Default university, can be changed later
          createdAt: new Date(),
          updatedAt: new Date()
        };
        targetCollection = COLLECTIONS.ASSISTANTS;
        newRole = 'assistant';
        console.log('Creating new assistant account for:', email);
      } else {
        // Email not authorized for automatic account creation
        console.log('Unauthorized Google login attempt for:', email);
        return res.status(401).json({
          message: 'Access denied. Your Google account is not authorized for this system. Please contact your administrator.'
        });
      }

      // Create the new user account
      if (newUserData && targetCollection && newRole) {
        foundUser = await FirestoreHelpers.create(targetCollection, newUserData);
        role = newRole;
        console.log(`Successfully created new ${newRole} account for:`, email);
      }
    }

    // Update user with Google info if not already present
    if (!foundUser.googleId) {
      const updateData = {
        googleId,
        picture,
        updatedAt: new Date()
      };

      const collection = role === 'superadmin' ? COLLECTIONS.CONFIGS :
                        role === 'student' ? COLLECTIONS.STUDENTS :
                        role === 'supervisor' ? COLLECTIONS.SUPERVISORS :
                        role === 'admin' ? COLLECTIONS.ADMINS :
                        COLLECTIONS.ASSISTANTS;

      await FirestoreHelpers.update(collection, foundUser.id, updateData);
      foundUser = { ...foundUser, ...updateData };
    }

    console.log('Signing JWT for Google user:', { id: foundUser.id, role });
    const jwtToken = jwt.sign(
      { id: foundUser.id, role },
      config.JWT_SECRET,
      { expiresIn: config.JWT_ACCESS_EXPIRATION }
    );

    // Prepare user response
    const userResponse = {
      id: foundUser.id,
      email: foundUser.email,
      name: foundUser.name,
      role,
      university: foundUser.university || '',
      picture: foundUser.picture,
      loginMethod: 'google'
    };

    // Add role-specific fields
    if (role === 'student') {
      userResponse.studentId = foundUser.studentId || foundUser.id;
    } else if (role === 'assistant') {
      userResponse.dentistId = foundUser.dentistId;
      if (foundUser.affiliation) {
        userResponse.affiliation = foundUser.affiliation;
      }
    }

    // Log successful login
    try {
      await FirestoreHelpers.create(COLLECTIONS.ACTIVITY_LOGS, {
        userId: foundUser.id,
        userName: foundUser.name,
        userRole: role,
        action: 'User logged in via Google',
        details: `Email: ${email}`,
        ipAddress: req.ip || req.connection.remoteAddress || 'Unknown',
        timestamp: new Date()
      });
    } catch (logError) {
      console.error('Error logging Google login activity:', logError);
    }

    res.json({
      token: jwtToken,
      role,
      user: userResponse,
    });
  } catch (err) {
    console.error('Google login error:', err);
    if (err.message && err.message.includes('Token used too late')) {
      return res.status(401).json({ message: 'Google token has expired. Please try again.' });
    }
    res.status(500).json({ message: 'Server error during Google authentication' });
  }
};

const getCurrentUser = async (req, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'User not authenticated' });
    }

    const userData = {
      id: req.user.id,
      role: req.user.role,
      name: req.user.name,
      email: req.user.email,
      university: req.user.university || '',
    };
    if (req.user.role === 'student') {
      userData.studentId = req.user.studentId || req.user.id;
    }

    res.json(userData);
  } catch (error) {
    console.error('getCurrentUser error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get user profile
const getProfile = async (req, res) => {
  try {
    const { id, role } = req.user;
    let user;

    // Fetch user based on role
    if (role === 'student') {
      user = await FirestoreHelpers.findById(COLLECTIONS.STUDENTS, id);
    } else if (role === 'supervisor') {
      user = await FirestoreHelpers.findById(COLLECTIONS.SUPERVISORS, id);
    } else if (role === 'admin') {
      user = await FirestoreHelpers.findById(COLLECTIONS.ADMINS, id);
    } else if (role === 'assistant') {
      user = await FirestoreHelpers.findById(COLLECTIONS.ASSISTANTS, id);
    } else if (role === 'superadmin') {
      user = await FirestoreHelpers.findById(COLLECTIONS.CONFIGS, id);
    }

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Remove password from response
    const userData = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: role,
      university: user.university || '',
      studentId: role === 'student' ? user.studentId || user.id : undefined,
    };

    res.json(userData);
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ message: error.message });
  }
};

// Get user role endpoint
const getUserRole = async (req, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'User not authenticated' });
    }
    res.json({ role: req.user.role });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

module.exports = { login, googleLogin, getCurrentUser, getProfile, getUserRole };