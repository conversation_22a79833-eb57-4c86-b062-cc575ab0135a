{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\superadmin\\\\Accounts.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { motion } from 'framer-motion';\nimport { FaUsers, FaPlus, FaSearch, FaEdit, FaTrash } from 'react-icons/fa';\nimport Navbar from '../student/Navbar';\nimport Loader from '../components/Loader';\nimport ConfirmModal from '../components/ConfirmModal';\nimport SuccessModal from '../components/SuccessModal';\nimport { useAuth } from '../context/AuthContext';\nimport SuperAdminSidebar from './SuperAdminSidebar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Accounts = () => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [accounts, setAccounts] = useState([]);\n  const [filteredAccounts, setFilteredAccounts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [showModal, setShowModal] = useState(false);\n  const [showDetailsModal, setShowDetailsModal] = useState(false);\n  const [selectedAccount, setSelectedAccount] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [roleFilter, setRoleFilter] = useState('');\n  const [universities, setUniversities] = useState([]);\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\n  const [selectedAccountId, setSelectedAccountId] = useState(null);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [successTitle, setSuccessTitle] = useState('');\n  const [successMessage, setSuccessMessage] = useState('');\n  const [showResetPasswordModal, setShowResetPasswordModal] = useState(false);\n  const [resetPasswordId, setResetPasswordId] = useState(null);\n  const [newPassword, setNewPassword] = useState('');\n  const [showResetAllPasswordsModal, setShowResetAllPasswordsModal] = useState(false);\n  const [defaultPassword, setDefaultPassword] = useState('');\n  const [showResetPassword, setShowResetPassword] = useState(false);\n  const [showDefaultPassword, setShowDefaultPassword] = useState(false);\n  // Password display removed as requested\n  const navigate = useNavigate();\n  const {\n    user,\n    token\n  } = useAuth();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    name: '',\n    role: 'student',\n    studentId: '',\n    universityId: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  useEffect(() => {\n    const fetchAccounts = async () => {\n      if (!user || !token) {\n        setError('Please log in to view accounts.');\n        setLoading(false);\n        return;\n      }\n      try {\n        const config = {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        };\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/accounts`, config);\n        setAccounts(response.data || []);\n        setFilteredAccounts(response.data || []);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response3$data, _err$response4;\n        console.error('Fetch error:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n        const errorMessage = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.status) === 401 ? 'Unauthorized. Please log in again.' : ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || 'Failed to load accounts';\n        setError(errorMessage);\n        if (((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 401) {\n          navigate('/login');\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchAccounts();\n  }, [user, token, navigate]);\n  useEffect(() => {\n    const fetchUniversities = async () => {\n      if (!user || !token) return;\n      try {\n        const config = {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        };\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/universities`, config);\n        setUniversities(response.data || []);\n      } catch (err) {\n        var _err$response5;\n        console.error('Error fetching universities:', ((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : _err$response5.data) || err.message);\n      }\n    };\n    fetchUniversities();\n  }, [user, token]);\n  useEffect(() => {\n    const filtered = accounts.filter(account => {\n      const matchesSearch = account.email.toLowerCase().includes(searchTerm.toLowerCase()) || account.name.toLowerCase().includes(searchTerm.toLowerCase());\n      const matchesRole = roleFilter ? account.role === roleFilter : true;\n      return matchesSearch && matchesRole;\n    });\n    setFilteredAccounts(filtered);\n  }, [searchTerm, roleFilter, accounts]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      // Validate form based on role\n      if (formData.role === 'student' && !formData.studentId) {\n        setError('Student ID is required for student accounts');\n        return;\n      }\n      if (['student', 'supervisor', 'admin', 'assistant'].includes(formData.role) && !formData.universityId) {\n        setError('University ID is required for this role');\n        return;\n      }\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      // Clean formData based on role\n      const cleanedData = {\n        ...formData\n      };\n      if (formData.role !== 'student') {\n        delete cleanedData.studentId;\n      }\n\n      // Log the data being sent\n      console.log('Creating account with data:', {\n        ...cleanedData,\n        password: cleanedData.password.substring(0, 3) + '***' // Only log first 3 chars of password\n      });\n      await axios.post(`${process.env.REACT_APP_API_URL}/api/accounts`, cleanedData, config);\n      setShowModal(false);\n      setFormData({\n        email: '',\n        password: '',\n        name: '',\n        role: 'student',\n        studentId: '',\n        universityId: ''\n      });\n      setError('');\n      // Refresh accounts\n      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/accounts`, config);\n      setAccounts(response.data || []);\n\n      // Show success message\n      setSuccessTitle('Account Added');\n      setSuccessMessage('Account has been added successfully!');\n      setShowSuccessModal(true);\n    } catch (err) {\n      var _err$response6, _err$response6$data;\n      setError(((_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.message) || 'Failed to create account');\n    }\n  };\n  const handleAccountClick = account => {\n    setSelectedAccount(account);\n    setShowDetailsModal(true);\n  };\n  const handleDeleteClick = (e, accountId) => {\n    e.stopPropagation();\n    setSelectedAccountId(accountId);\n    setShowConfirmModal(true);\n  };\n  const handleResetPasswordClick = (e, accountId) => {\n    e.stopPropagation();\n    setResetPasswordId(accountId);\n    setNewPassword('');\n    setShowResetPasswordModal(true);\n  };\n  const handleResetPassword = async () => {\n    try {\n      if (!newPassword || newPassword.length < 6) {\n        setError('Password must be at least 6 characters');\n        return;\n      }\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      await axios.post(`${process.env.REACT_APP_API_URL}/api/accounts/${resetPasswordId}/reset-password`, {\n        newPassword\n      }, config);\n      setShowResetPasswordModal(false);\n      setNewPassword('');\n\n      // Show success message\n      setSuccessTitle('Password Reset');\n      setSuccessMessage('Password has been reset successfully!');\n      setShowSuccessModal(true);\n    } catch (err) {\n      var _err$response7, _err$response7$data;\n      setError(((_err$response7 = err.response) === null || _err$response7 === void 0 ? void 0 : (_err$response7$data = _err$response7.data) === null || _err$response7$data === void 0 ? void 0 : _err$response7$data.message) || 'Failed to reset password');\n    }\n  };\n  const handleResetAllPasswords = async () => {\n    try {\n      if (!defaultPassword || defaultPassword.length < 6) {\n        setError('Password must be at least 6 characters');\n        return;\n      }\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/auth/reset-all-passwords`, {\n        defaultPassword\n      }, config);\n      setShowResetAllPasswordsModal(false);\n      setDefaultPassword('');\n\n      // Show success message\n      setSuccessTitle('All Passwords Reset');\n      setSuccessMessage(`All passwords have been reset successfully! ${JSON.stringify(response.data.results)}`);\n      setShowSuccessModal(true);\n    } catch (err) {\n      var _err$response8, _err$response8$data;\n      setError(((_err$response8 = err.response) === null || _err$response8 === void 0 ? void 0 : (_err$response8$data = _err$response8.data) === null || _err$response8$data === void 0 ? void 0 : _err$response8$data.message) || 'Failed to reset all passwords');\n    }\n  };\n  const handleConfirmDelete = async () => {\n    try {\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      await axios.delete(`${process.env.REACT_APP_API_URL}/api/accounts/${selectedAccountId}`, config);\n      setAccounts(accounts.filter(a => a.id !== selectedAccountId));\n      setShowConfirmModal(false);\n      setSelectedAccountId(null);\n\n      // Show success message\n      setSuccessTitle('Account Deleted');\n      setSuccessMessage('Account has been deleted successfully!');\n      setShowSuccessModal(true);\n    } catch (err) {\n      var _err$response9, _err$response9$data;\n      setError(((_err$response9 = err.response) === null || _err$response9 === void 0 ? void 0 : (_err$response9$data = _err$response9.data) === null || _err$response9$data === void 0 ? void 0 : _err$response9$data.message) || 'Failed to delete account');\n      setShowConfirmModal(false);\n    }\n  };\n  const container = {\n    hidden: {\n      opacity: 0\n    },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(SuperAdminSidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [error && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-5 w-5 text-red-500 mr-3\",\n                viewBox: \"0 0 20 20\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-700 font-medium\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\",\n                  children: \"Accounts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#333333]\",\n                  children: \"Manage user accounts and permissions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-3 w-full md:w-auto\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => {\n                    setDefaultPassword('');\n                    setShowResetAllPasswordsModal(true);\n                  },\n                  className: \"w-full md:w-auto bg-gradient-to-r from-yellow-500 to-yellow-600 text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5 mr-2\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 315,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 21\n                  }, this), \"Reset All Passwords\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => setShowModal(true),\n                  className: \"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n                    className: \"h-5 w-5 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 21\n                  }, this), \"Add Account\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6 flex flex-col sm:flex-row gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(FaSearch, {\n                  className: \"absolute left-3 top-3 h-5 w-5 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search by email or name...\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value),\n                  className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: roleFilter,\n                onChange: e => setRoleFilter(e.target.value),\n                className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Roles\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"student\",\n                  children: \"Student\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"supervisor\",\n                  children: \"Supervisor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"admin\",\n                  children: \"Admin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"assistant\",\n                  children: \"Assistant\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              animate: \"show\",\n              // Force re-render when accounts change\n              className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"overflow-x-auto\",\n                  children: /*#__PURE__*/_jsxDEV(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                      className: \"bg-gray-50\",\n                      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Email\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 367,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Name\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 368,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Role\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 369,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"University ID\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 370,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Student ID\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 371,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Actions\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 373,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 366,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 365,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                      className: \"bg-white divide-y divide-gray-200\",\n                      children: filteredAccounts.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: /*#__PURE__*/_jsxDEV(\"td\", {\n                          colSpan: \"7\",\n                          className: \"px-6 py-8 text-center\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col items-center justify-center\",\n                            children: [/*#__PURE__*/_jsxDEV(FaUsers, {\n                              className: \"h-12 w-12 text-gray-400 mb-4\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 381,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                              className: \"text-lg font-medium text-gray-900\",\n                              children: \"No accounts found\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 382,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"mt-1 text-gray-500\",\n                              children: \"Add an account or adjust filters.\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 383,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 380,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 379,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 378,\n                        columnNumber: 27\n                      }, this) : filteredAccounts.map((account, index) => /*#__PURE__*/_jsxDEV(motion.tr, {\n                        // Use account.id for unique key\n                        variants: item,\n                        className: \"hover:bg-gray-50 cursor-pointer\",\n                        onClick: () => handleAccountClick(account),\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                          children: account.email\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 395,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                          children: account.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 396,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                          children: account.role\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 397,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                          children: account.universityId || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 398,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                          children: account.studentId || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 399,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: e => handleResetPasswordClick(e, account.id),\n                              className: \"text-[#0077B6] hover:text-[#20B2AA]\",\n                              title: \"Reset Password\",\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"h-5 w-5\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  strokeLinecap: \"round\",\n                                  strokeLinejoin: \"round\",\n                                  strokeWidth: 2,\n                                  d: \"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 409,\n                                  columnNumber: 39\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 408,\n                                columnNumber: 37\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 403,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: e => handleDeleteClick(e, account.id),\n                              className: \"text-red-600 hover:text-red-800\",\n                              title: \"Delete\",\n                              children: /*#__PURE__*/_jsxDEV(FaTrash, {\n                                className: \"h-5 w-5\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 417,\n                                columnNumber: 37\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 412,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 402,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 401,\n                          columnNumber: 31\n                        }, this)]\n                      }, account.id || account.email + index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 389,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 17\n              }, this)\n            }, accounts.length, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-[#0077B6]\",\n              children: \"Add Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowModal(false),\n              className: \"text-gray-400 hover:text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Email*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  value: formData.email,\n                  onChange: handleInputChange,\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Password*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: showPassword ? \"text\" : \"password\",\n                    name: \"password\",\n                    value: formData.password,\n                    onChange: handleInputChange,\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] pr-10\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700\",\n                    onClick: () => setShowPassword(!showPassword),\n                    children: showPassword ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: \"h-5 w-5\",\n                      viewBox: \"0 0 20 20\",\n                      fill: \"currentColor\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 481,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 482,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 480,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: \"h-5 w-5\",\n                      viewBox: \"0 0 20 20\",\n                      fill: \"currentColor\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 486,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 487,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 485,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Name*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"name\",\n                  value: formData.name,\n                  onChange: handleInputChange,\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Role*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"role\",\n                  value: formData.role,\n                  onChange: handleInputChange,\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"student\",\n                    children: \"Student\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 513,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"supervisor\",\n                    children: \"Supervisor\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"admin\",\n                    children: \"Admin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"assistant\",\n                    children: \"Assistant\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 516,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 19\n              }, this), formData.role === 'student' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Student ID*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"studentId\",\n                  value: formData.studentId,\n                  onChange: handleInputChange,\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 21\n              }, this), ['student', 'supervisor', 'admin', 'assistant'].includes(formData.role) && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"University*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"universityId\",\n                  value: formData.universityId,\n                  onChange: handleInputChange,\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select University\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 542,\n                    columnNumber: 25\n                  }, this), universities.map(university => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: university.universityId,\n                    children: [university.name.en, \" (\", university.universityId, \")\"]\n                  }, university.universityId, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 544,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-4 pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                type: \"button\",\n                onClick: () => setShowModal(false),\n                className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\",\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                type: \"submit\",\n                className: \"px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 font-medium transition-colors shadow-md hover:shadow-lg\",\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: \"Add Account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 435,\n      columnNumber: 9\n    }, this), showDetailsModal && selectedAccount && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-2xl shadow-2xl w-full max-w-2xl\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-blue-500 to-blue-700 text-white p-3 rounded-full mr-4\",\n                children: [selectedAccount.role === 'student' && /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-6 w-6\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 14l9-5-9-5-9 5 9 5z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 592,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 23\n                }, this), selectedAccount.role === 'supervisor' && /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-6 w-6\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 23\n                }, this), selectedAccount.role === 'admin' && /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-6 w-6\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 605,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 23\n                }, this), selectedAccount.role === 'assistant' && /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-6 w-6\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 23\n                }, this), selectedAccount.role === 'dentist' && /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-6 w-6\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 23\n                }, this), selectedAccount.role === 'superadmin' && /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-6 w-6\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 620,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-bold text-blue-900\",\n                  children: selectedAccount.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-500 capitalize\",\n                  children: selectedAccount.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 626,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowDetailsModal(false),\n              className: \"text-gray-400 hover:text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 p-6 rounded-xl mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-800 mb-4\",\n              children: \"Account Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-gray-900 font-medium\",\n                  children: selectedAccount.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 641,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 644,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-gray-900 font-medium\",\n                  children: selectedAccount.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 645,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Role\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-gray-900 font-medium capitalize\",\n                  children: selectedAccount.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Created At\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-gray-900 font-medium\",\n                  children: selectedAccount.createdAt ? new Date(selectedAccount.createdAt).toLocaleString() : 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Last Updated\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-gray-900 font-medium\",\n                  children: selectedAccount.updatedAt ? new Date(selectedAccount.updatedAt).toLocaleString() : 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 660,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 p-6 rounded-xl mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-800 mb-4\",\n              children: \"Role-Specific Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 668,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [selectedAccount.universityId && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"University ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 672,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-gray-900 font-medium\",\n                  children: selectedAccount.universityId\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 673,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 21\n              }, this), selectedAccount.studentId && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Student ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-gray-900 font-medium\",\n                  children: selectedAccount.studentId\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 679,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              onClick: () => {\n                setResetPasswordId(selectedAccount.id);\n                setNewPassword('');\n                setShowResetPassword(false);\n                setShowDetailsModal(false);\n                setShowResetPasswordModal(true);\n              },\n              className: \"px-6 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 font-medium transition-colors flex items-center\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-5 w-5 mr-2\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 19\n              }, this), \"Reset Password\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              onClick: () => setShowDetailsModal(false),\n              className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"Close\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 704,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 581,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 580,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmModal, {\n      isOpen: showConfirmModal,\n      onClose: () => setShowConfirmModal(false),\n      onConfirm: handleConfirmDelete,\n      title: \"Confirm Delete\",\n      message: \"Are you sure you want to delete this account? This action cannot be undone.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 719,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SuccessModal, {\n      isOpen: showSuccessModal,\n      onClose: () => setShowSuccessModal(false),\n      title: successTitle,\n      message: successMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 728,\n      columnNumber: 7\n    }, this), showResetPasswordModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-2xl shadow-2xl w-full max-w-md\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-blue-900\",\n              children: \"Reset Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 745,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowResetPasswordModal(false),\n              className: \"text-gray-400 hover:text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 748,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 747,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 744,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"New Password*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: showResetPassword ? \"text\" : \"password\",\n                  value: newPassword,\n                  onChange: e => setNewPassword(e.target.value),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-10\",\n                  placeholder: \"Enter new password\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 756,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700\",\n                  onClick: () => setShowResetPassword(!showResetPassword),\n                  children: showResetPassword ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5\",\n                    viewBox: \"0 0 20 20\",\n                    fill: \"currentColor\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 771,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 772,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 770,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5\",\n                    viewBox: \"0 0 20 20\",\n                    fill: \"currentColor\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 776,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 777,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 775,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 764,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-gray-500\",\n                children: \"Password must be at least 6 characters.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-4 mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              type: \"button\",\n              onClick: () => setShowResetPasswordModal(false),\n              className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              type: \"button\",\n              onClick: handleResetPassword,\n              className: \"px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 font-medium transition-colors shadow-md hover:shadow-lg\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"Reset Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 795,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 785,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 743,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 738,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 737,\n      columnNumber: 9\n    }, this), showResetAllPasswordsModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-2xl shadow-2xl w-full max-w-md\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-yellow-600\",\n              children: \"Reset All Passwords\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 820,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowResetAllPasswordsModal(false),\n              className: \"text-gray-400 hover:text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 823,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 821,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 819,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-700 mb-4\",\n                children: [\"This will reset the passwords for \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"ALL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 830,\n                  columnNumber: 55\n                }, this), \" accounts in the system. Are you sure you want to continue?\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 829,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Default Password*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 833,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: showDefaultPassword ? \"text\" : \"password\",\n                  value: defaultPassword,\n                  onChange: e => setDefaultPassword(e.target.value),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-10\",\n                  placeholder: \"Enter default password for all accounts\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 835,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700\",\n                  onClick: () => setShowDefaultPassword(!showDefaultPassword),\n                  children: showDefaultPassword ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5\",\n                    viewBox: \"0 0 20 20\",\n                    fill: \"currentColor\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 850,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 851,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 849,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5\",\n                    viewBox: \"0 0 20 20\",\n                    fill: \"currentColor\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 855,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 856,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 854,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 843,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 834,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-gray-500\",\n                children: \"Password must be at least 6 characters.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 861,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 828,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-4 mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              type: \"button\",\n              onClick: () => setShowResetAllPasswordsModal(false),\n              className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 865,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              type: \"button\",\n              onClick: handleResetAllPasswords,\n              className: \"px-6 py-2 bg-gradient-to-r from-yellow-500 to-yellow-700 text-white rounded-lg hover:from-yellow-600 hover:to-yellow-800 font-medium transition-colors shadow-md hover:shadow-lg\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"Reset All Passwords\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 874,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 864,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 818,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 813,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 812,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 271,\n    columnNumber: 5\n  }, this);\n};\n_s(Accounts, \"b4GkQuYqlvSq8kPpEt7v0uNcO00=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Accounts;\nexport default Accounts;\nvar _c;\n$RefreshReg$(_c, \"Accounts\");", "map": {"version": 3, "names": ["useState", "useEffect", "useNavigate", "axios", "motion", "FaUsers", "FaPlus", "FaSearch", "FaEdit", "FaTrash", "<PERSON><PERSON><PERSON>", "Loader", "ConfirmModal", "SuccessModal", "useAuth", "SuperAdminSidebar", "jsxDEV", "_jsxDEV", "Accounts", "_s", "sidebarOpen", "setSidebarOpen", "accounts", "setAccounts", "filteredAccounts", "setFilteredAccounts", "loading", "setLoading", "error", "setError", "showModal", "setShowModal", "showDetailsModal", "setShowDetailsModal", "selectedAccount", "setSelectedAccount", "searchTerm", "setSearchTerm", "<PERSON><PERSON><PERSON>er", "setRoleFilter", "universities", "setUniversities", "showConfirmModal", "setShowConfirmModal", "selectedAccountId", "setSelectedAccountId", "showSuccessModal", "setShowSuccessModal", "successTitle", "setSuccessTitle", "successMessage", "setSuccessMessage", "showResetPasswordModal", "setShowResetPasswordModal", "resetPasswordId", "setResetPasswordId", "newPassword", "setNewPassword", "showResetAllPasswordsModal", "setShowResetAllPasswordsModal", "defaultPassword", "setDefaultPassword", "showResetPassword", "setShowResetPassword", "showDefaultPassword", "setShowDefaultPassword", "navigate", "user", "token", "formData", "setFormData", "email", "password", "name", "role", "studentId", "universityId", "showPassword", "setShowPassword", "fetchAccounts", "config", "headers", "Authorization", "response", "get", "process", "env", "REACT_APP_API_URL", "data", "err", "_err$response", "_err$response2", "_err$response3", "_err$response3$data", "_err$response4", "console", "message", "errorMessage", "status", "fetchUniversities", "_err$response5", "filtered", "filter", "account", "matchesSearch", "toLowerCase", "includes", "matchesRole", "handleInputChange", "e", "value", "target", "handleSubmit", "preventDefault", "cleanedData", "log", "substring", "post", "_err$response6", "_err$response6$data", "handleAccountClick", "handleDeleteClick", "accountId", "stopPropagation", "handleResetPasswordClick", "handleResetPassword", "length", "_err$response7", "_err$response7$data", "handleResetAllPasswords", "JSON", "stringify", "results", "_err$response8", "_err$response8$data", "handleConfirmDelete", "delete", "a", "id", "_err$response9", "_err$response9$data", "container", "hidden", "opacity", "show", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "item", "y", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "isOpen", "setIsOpen", "toggleSidebar", "div", "initial", "animate", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "duration", "button", "whileHover", "scale", "whileTap", "onClick", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "type", "placeholder", "onChange", "variants", "colSpan", "map", "index", "tr", "title", "onSubmit", "required", "university", "en", "createdAt", "Date", "toLocaleString", "updatedAt", "onClose", "onConfirm", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/superadmin/Accounts.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { motion } from 'framer-motion';\nimport { FaUsers, FaPlus, FaSearch, FaEdit, FaTrash } from 'react-icons/fa';\nimport Navbar from '../student/Navbar';\nimport Loader from '../components/Loader';\nimport ConfirmModal from '../components/ConfirmModal';\nimport SuccessModal from '../components/SuccessModal';\nimport { useAuth } from '../context/AuthContext';\nimport SuperAdminSidebar from './SuperAdminSidebar';\n\nconst Accounts = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [accounts, setAccounts] = useState([]);\n  const [filteredAccounts, setFilteredAccounts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [showModal, setShowModal] = useState(false);\n  const [showDetailsModal, setShowDetailsModal] = useState(false);\n  const [selectedAccount, setSelectedAccount] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [roleFilter, setRoleFilter] = useState('');\n  const [universities, setUniversities] = useState([]);\n\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\n  const [selectedAccountId, setSelectedAccountId] = useState(null);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [successTitle, setSuccessTitle] = useState('');\n  const [successMessage, setSuccessMessage] = useState('');\n  const [showResetPasswordModal, setShowResetPasswordModal] = useState(false);\n  const [resetPasswordId, setResetPasswordId] = useState(null);\n  const [newPassword, setNewPassword] = useState('');\n  const [showResetAllPasswordsModal, setShowResetAllPasswordsModal] = useState(false);\n  const [defaultPassword, setDefaultPassword] = useState('');\n  const [showResetPassword, setShowResetPassword] = useState(false);\n  const [showDefaultPassword, setShowDefaultPassword] = useState(false);\n  // Password display removed as requested\n  const navigate = useNavigate();\n  const { user, token } = useAuth();\n\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    name: '',\n    role: 'student',\n    studentId: '',\n    universityId: '',\n\n  });\n\n  const [showPassword, setShowPassword] = useState(false);\n\n  useEffect(() => {\n    const fetchAccounts = async () => {\n      if (!user || !token) {\n        setError('Please log in to view accounts.');\n        setLoading(false);\n        return;\n      }\n\n      try {\n        const config = { headers: { Authorization: `Bearer ${token}` } };\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/accounts`, config);\n        setAccounts(response.data || []);\n        setFilteredAccounts(response.data || []);\n      } catch (err) {\n        console.error('Fetch error:', err.response?.data || err.message);\n        const errorMessage = err.response?.status === 401\n          ? 'Unauthorized. Please log in again.'\n          : err.response?.data?.message || 'Failed to load accounts';\n        setError(errorMessage);\n        if (err.response?.status === 401) {\n          navigate('/login');\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchAccounts();\n  }, [user, token, navigate]);\n\n  useEffect(() => {\n    const fetchUniversities = async () => {\n      if (!user || !token) return;\n\n      try {\n        const config = { headers: { Authorization: `Bearer ${token}` } };\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/universities`, config);\n        setUniversities(response.data || []);\n      } catch (err) {\n        console.error('Error fetching universities:', err.response?.data || err.message);\n      }\n    };\n\n    fetchUniversities();\n  }, [user, token]);\n\n\n\n  useEffect(() => {\n    const filtered = accounts.filter(account => {\n      const matchesSearch = account.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                           account.name.toLowerCase().includes(searchTerm.toLowerCase());\n      const matchesRole = roleFilter ? account.role === roleFilter : true;\n      return matchesSearch && matchesRole;\n    });\n    setFilteredAccounts(filtered);\n  }, [searchTerm, roleFilter, accounts]);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({ ...formData, [name]: value });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      // Validate form based on role\n      if (formData.role === 'student' && !formData.studentId) {\n        setError('Student ID is required for student accounts');\n        return;\n      }\n\n      if (['student', 'supervisor', 'admin', 'assistant'].includes(formData.role) && !formData.universityId) {\n        setError('University ID is required for this role');\n        return;\n      }\n\n      const config = { headers: { Authorization: `Bearer ${token}` } };\n      // Clean formData based on role\n      const cleanedData = { ...formData };\n      if (formData.role !== 'student') {\n        delete cleanedData.studentId;\n      }\n\n\n\n      // Log the data being sent\n      console.log('Creating account with data:', {\n        ...cleanedData,\n        password: cleanedData.password.substring(0, 3) + '***' // Only log first 3 chars of password\n      });\n\n      await axios.post(`${process.env.REACT_APP_API_URL}/api/accounts`, cleanedData, config);\n      setShowModal(false);\n      setFormData({\n        email: '',\n        password: '',\n        name: '',\n        role: 'student',\n        studentId: '',\n        universityId: '',\n\n      });\n      setError('');\n      // Refresh accounts\n      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/accounts`, config);\n      setAccounts(response.data || []);\n\n      // Show success message\n      setSuccessTitle('Account Added');\n      setSuccessMessage('Account has been added successfully!');\n      setShowSuccessModal(true);\n    } catch (err) {\n      setError(err.response?.data?.message || 'Failed to create account');\n    }\n  };\n\n  const handleAccountClick = (account) => {\n    setSelectedAccount(account);\n    setShowDetailsModal(true);\n  };\n\n  const handleDeleteClick = (e, accountId) => {\n    e.stopPropagation();\n    setSelectedAccountId(accountId);\n    setShowConfirmModal(true);\n  };\n\n  const handleResetPasswordClick = (e, accountId) => {\n    e.stopPropagation();\n    setResetPasswordId(accountId);\n    setNewPassword('');\n    setShowResetPasswordModal(true);\n  };\n\n  const handleResetPassword = async () => {\n    try {\n      if (!newPassword || newPassword.length < 6) {\n        setError('Password must be at least 6 characters');\n        return;\n      }\n\n      const config = { headers: { Authorization: `Bearer ${token}` } };\n      await axios.post(`${process.env.REACT_APP_API_URL}/api/accounts/${resetPasswordId}/reset-password`,\n        { newPassword },\n        config\n      );\n\n      setShowResetPasswordModal(false);\n      setNewPassword('');\n\n      // Show success message\n      setSuccessTitle('Password Reset');\n      setSuccessMessage('Password has been reset successfully!');\n      setShowSuccessModal(true);\n    } catch (err) {\n      setError(err.response?.data?.message || 'Failed to reset password');\n    }\n  };\n\n  const handleResetAllPasswords = async () => {\n    try {\n      if (!defaultPassword || defaultPassword.length < 6) {\n        setError('Password must be at least 6 characters');\n        return;\n      }\n\n      const config = { headers: { Authorization: `Bearer ${token}` } };\n      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/auth/reset-all-passwords`,\n        { defaultPassword },\n        config\n      );\n\n      setShowResetAllPasswordsModal(false);\n      setDefaultPassword('');\n\n      // Show success message\n      setSuccessTitle('All Passwords Reset');\n      setSuccessMessage(`All passwords have been reset successfully! ${JSON.stringify(response.data.results)}`);\n      setShowSuccessModal(true);\n    } catch (err) {\n      setError(err.response?.data?.message || 'Failed to reset all passwords');\n    }\n  };\n\n  const handleConfirmDelete = async () => {\n    try {\n      const config = { headers: { Authorization: `Bearer ${token}` } };\n      await axios.delete(`${process.env.REACT_APP_API_URL}/api/accounts/${selectedAccountId}`, config);\n      setAccounts(accounts.filter((a) => a.id !== selectedAccountId));\n      setShowConfirmModal(false);\n      setSelectedAccountId(null);\n\n      // Show success message\n      setSuccessTitle('Account Deleted');\n      setSuccessMessage('Account has been deleted successfully!');\n      setShowSuccessModal(true);\n    } catch (err) {\n      setError(err.response?.data?.message || 'Failed to delete account');\n      setShowConfirmModal(false);\n    }\n  };\n\n  const container = {\n    hidden: { opacity: 0 },\n    show: { opacity: 1, transition: { staggerChildren: 0.1 } },\n  };\n\n  const item = {\n    hidden: { opacity: 0, y: 20 },\n    show: { opacity: 1, y: 0 },\n  };\n\n  if (loading) {\n    return <Loader />;\n  }\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      <SuperAdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\n\n        <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\">\n          <div className=\"max-w-7xl mx-auto\">\n            {error && (\n              <motion.div\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\"\n              >\n                <div className=\"flex items-center\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-red-500 mr-3\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <p className=\"text-red-700 font-medium\">{error}</p>\n                </div>\n              </motion.div>\n            )}\n\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5 }}\n            >\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\n                <div>\n                  <h1 className=\"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\">Accounts</h1>\n                  <p className=\"text-[#333333]\">Manage user accounts and permissions</p>\n                </div>\n                <div className=\"flex flex-col sm:flex-row gap-3 w-full md:w-auto\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => {\n                      setDefaultPassword('');\n                      setShowResetAllPasswordsModal(true);\n                    }}\n                    className=\"w-full md:w-auto bg-gradient-to-r from-yellow-500 to-yellow-600 text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\"\n                  >\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\" />\n                    </svg>\n                    Reset All Passwords\n                  </motion.button>\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => setShowModal(true)}\n                    className=\"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\"\n                  >\n                    <FaPlus className=\"h-5 w-5 mr-2\" />\n                    Add Account\n                  </motion.button>\n                </div>\n              </div>\n\n              <div className=\"mb-6 flex flex-col sm:flex-row gap-4\">\n                <div className=\"relative flex-1\">\n                  <FaSearch className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search by email or name...\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                  />\n                </div>\n                <select\n                  value={roleFilter}\n                  onChange={(e) => setRoleFilter(e.target.value)}\n                  className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                >\n                  <option value=\"\">All Roles</option>\n                  <option value=\"student\">Student</option>\n                  <option value=\"supervisor\">Supervisor</option>\n                  <option value=\"admin\">Admin</option>\n                  <option value=\"assistant\">Assistant</option>\n                </select>\n              </div>\n\n              <motion.div\n                variants={container}\n                initial=\"hidden\"\n                animate=\"show\"\n                key={accounts.length} // Force re-render when accounts change\n                className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden\"\n              >\n                <div className=\"p-6\">\n                  <div className=\"overflow-x-auto\">\n                    <table className=\"min-w-full divide-y divide-gray-200\">\n                      <thead className=\"bg-gray-50\">\n                        <tr>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Email</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Name</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Role</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">University ID</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Student ID</th>\n\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n                        </tr>\n                      </thead>\n                      <tbody className=\"bg-white divide-y divide-gray-200\">\n                        {filteredAccounts.length === 0 ? (\n                          <tr>\n                            <td colSpan=\"7\" className=\"px-6 py-8 text-center\">\n                              <div className=\"flex flex-col items-center justify-center\">\n                                <FaUsers className=\"h-12 w-12 text-gray-400 mb-4\" />\n                                <h3 className=\"text-lg font-medium text-gray-900\">No accounts found</h3>\n                                <p className=\"mt-1 text-gray-500\">Add an account or adjust filters.</p>\n                              </div>\n                            </td>\n                          </tr>\n                        ) : (\n                          filteredAccounts.map((account, index) => (\n                            <motion.tr\n                              key={account.id || account.email + index} // Use account.id for unique key\n                              variants={item}\n                              className=\"hover:bg-gray-50 cursor-pointer\"\n                              onClick={() => handleAccountClick(account)}\n                            >\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{account.email}</td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{account.name}</td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{account.role}</td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{account.universityId || 'N/A'}</td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{account.studentId || 'N/A'}</td>\n\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                                <div className=\"flex space-x-3\">\n                                  <button\n                                    onClick={(e) => handleResetPasswordClick(e, account.id)}\n                                    className=\"text-[#0077B6] hover:text-[#20B2AA]\"\n                                    title=\"Reset Password\"\n                                  >\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\" />\n                                    </svg>\n                                  </button>\n                                  <button\n                                    onClick={(e) => handleDeleteClick(e, account.id)}\n                                    className=\"text-red-600 hover:text-red-800\"\n                                    title=\"Delete\"\n                                  >\n                                    <FaTrash className=\"h-5 w-5\" />\n                                  </button>\n                                </div>\n                              </td>\n                            </motion.tr>\n                          ))\n                        )}\n                      </tbody>\n                    </table>\n                  </div>\n                </div>\n              </motion.div>\n            </motion.div>\n          </div>\n        </main>\n      </div>\n\n      {showModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <motion.div\n            initial={{ scale: 0.9, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            className=\"bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\"\n          >\n            <div className=\"p-6\">\n              <div className=\"flex justify-between items-center mb-6\">\n                <h2 className=\"text-2xl font-bold text-[#0077B6]\">Add Account</h2>\n                <button onClick={() => setShowModal(false)} className=\"text-gray-400 hover:text-gray-500\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Email*</label>\n                    <input\n                      type=\"email\"\n                      name=\"email\"\n                      value={formData.email}\n                      onChange={handleInputChange}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Password*</label>\n                    <div className=\"relative\">\n                      <input\n                        type={showPassword ? \"text\" : \"password\"}\n                        name=\"password\"\n                        value={formData.password}\n                        onChange={handleInputChange}\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] pr-10\"\n                        required\n                      />\n                      <button\n                        type=\"button\"\n                        className=\"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700\"\n                        onClick={() => setShowPassword(!showPassword)}\n                      >\n                        {showPassword ? (\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path d=\"M10 12a2 2 0 100-4 2 2 0 000 4z\" />\n                            <path fillRule=\"evenodd\" d=\"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\" clipRule=\"evenodd\" />\n                          </svg>\n                        ) : (\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fillRule=\"evenodd\" d=\"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z\" clipRule=\"evenodd\" />\n                            <path d=\"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z\" />\n                          </svg>\n                        )}\n                      </button>\n                    </div>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Name*</label>\n                    <input\n                      type=\"text\"\n                      name=\"name\"\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Role*</label>\n                    <select\n                      name=\"role\"\n                      value={formData.role}\n                      onChange={handleInputChange}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                      required\n                    >\n                      <option value=\"student\">Student</option>\n                      <option value=\"supervisor\">Supervisor</option>\n                      <option value=\"admin\">Admin</option>\n                      <option value=\"assistant\">Assistant</option>\n                    </select>\n                  </div>\n                  {formData.role === 'student' && (\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">Student ID*</label>\n                      <input\n                        type=\"text\"\n                        name=\"studentId\"\n                        value={formData.studentId}\n                        onChange={handleInputChange}\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        required\n                      />\n                    </div>\n                  )}\n                  {['student', 'supervisor', 'admin', 'assistant'].includes(formData.role) && (\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">University*</label>\n                      <select\n                        name=\"universityId\"\n                        value={formData.universityId}\n                        onChange={handleInputChange}\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        required\n                      >\n                        <option value=\"\">Select University</option>\n                        {universities.map((university) => (\n                          <option key={university.universityId} value={university.universityId}>\n                            {university.name.en} ({university.universityId})\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n                  )}\n\n\n                </div>\n                <div className=\"flex justify-end space-x-4 pt-4\">\n                  <motion.button\n                    type=\"button\"\n                    onClick={() => setShowModal(false)}\n                    className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    Cancel\n                  </motion.button>\n                  <motion.button\n                    type=\"submit\"\n                    className=\"px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 font-medium transition-colors shadow-md hover:shadow-lg\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    Add Account\n                  </motion.button>\n                </div>\n              </form>\n            </div>\n          </motion.div>\n        </div>\n      )}\n\n      {showDetailsModal && selectedAccount && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <motion.div\n            initial={{ scale: 0.9, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            className=\"bg-white rounded-2xl shadow-2xl w-full max-w-2xl\"\n          >\n            <div className=\"p-8\">\n              <div className=\"flex justify-between items-center mb-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"bg-gradient-to-r from-blue-500 to-blue-700 text-white p-3 rounded-full mr-4\">\n                    {selectedAccount.role === 'student' && (\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path d=\"M12 14l9-5-9-5-9 5 9 5z\" />\n                        <path d=\"M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z\" />\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222\" />\n                      </svg>\n                    )}\n                    {selectedAccount.role === 'supervisor' && (\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                      </svg>\n                    )}\n                    {selectedAccount.role === 'admin' && (\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                      </svg>\n                    )}\n                    {selectedAccount.role === 'assistant' && (\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                      </svg>\n                    )}\n                    {selectedAccount.role === 'dentist' && (\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                      </svg>\n                    )}\n                    {selectedAccount.role === 'superadmin' && (\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                      </svg>\n                    )}\n                  </div>\n                  <div>\n                    <h2 className=\"text-2xl font-bold text-blue-900\">{selectedAccount.name}</h2>\n                    <p className=\"text-gray-500 capitalize\">{selectedAccount.role}</p>\n                  </div>\n                </div>\n                <button onClick={() => setShowDetailsModal(false)} className=\"text-gray-400 hover:text-gray-500\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n\n              <div className=\"bg-gray-50 p-6 rounded-xl mb-6\">\n                <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Account Information</h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Email</label>\n                    <p className=\"mt-1 text-gray-900 font-medium\">{selectedAccount.email}</p>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">ID</label>\n                    <p className=\"mt-1 text-gray-900 font-medium\">{selectedAccount.id}</p>\n                  </div>\n                  {/* Password field removed as requested */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Role</label>\n                    <p className=\"mt-1 text-gray-900 font-medium capitalize\">{selectedAccount.role}</p>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Created At</label>\n                    <p className=\"mt-1 text-gray-900 font-medium\">\n                      {selectedAccount.createdAt ? new Date(selectedAccount.createdAt).toLocaleString() : 'N/A'}\n                    </p>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Last Updated</label>\n                    <p className=\"mt-1 text-gray-900 font-medium\">\n                      {selectedAccount.updatedAt ? new Date(selectedAccount.updatedAt).toLocaleString() : 'N/A'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-gray-50 p-6 rounded-xl mb-6\">\n                <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Role-Specific Information</h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  {selectedAccount.universityId && (\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">University ID</label>\n                      <p className=\"mt-1 text-gray-900 font-medium\">{selectedAccount.universityId}</p>\n                    </div>\n                  )}\n                  {selectedAccount.studentId && (\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Student ID</label>\n                      <p className=\"mt-1 text-gray-900 font-medium\">{selectedAccount.studentId}</p>\n                    </div>\n                  )}\n\n                </div>\n              </div>\n\n              <div className=\"flex justify-end space-x-4\">\n                <motion.button\n                  onClick={() => {\n                    setResetPasswordId(selectedAccount.id);\n                    setNewPassword('');\n                    setShowResetPassword(false);\n                    setShowDetailsModal(false);\n                    setShowResetPasswordModal(true);\n                  }}\n                  className=\"px-6 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 font-medium transition-colors flex items-center\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\" />\n                  </svg>\n                  Reset Password\n                </motion.button>\n                <motion.button\n                  onClick={() => setShowDetailsModal(false)}\n                  className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  Close\n                </motion.button>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      )}\n\n      {/* Confirm Delete Modal */}\n      <ConfirmModal\n        isOpen={showConfirmModal}\n        onClose={() => setShowConfirmModal(false)}\n        onConfirm={handleConfirmDelete}\n        title=\"Confirm Delete\"\n        message=\"Are you sure you want to delete this account? This action cannot be undone.\"\n      />\n\n      {/* Success Modal */}\n      <SuccessModal\n        isOpen={showSuccessModal}\n        onClose={() => setShowSuccessModal(false)}\n        title={successTitle}\n        message={successMessage}\n      />\n\n      {/* Reset Password Modal */}\n      {showResetPasswordModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <motion.div\n            initial={{ scale: 0.9, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            className=\"bg-white rounded-2xl shadow-2xl w-full max-w-md\"\n          >\n            <div className=\"p-6\">\n              <div className=\"flex justify-between items-center mb-6\">\n                <h2 className=\"text-2xl font-bold text-blue-900\">Reset Password</h2>\n                <button onClick={() => setShowResetPasswordModal(false)} className=\"text-gray-400 hover:text-gray-500\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">New Password*</label>\n                  <div className=\"relative\">\n                    <input\n                      type={showResetPassword ? \"text\" : \"password\"}\n                      value={newPassword}\n                      onChange={(e) => setNewPassword(e.target.value)}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-10\"\n                      placeholder=\"Enter new password\"\n                      required\n                    />\n                    <button\n                      type=\"button\"\n                      className=\"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700\"\n                      onClick={() => setShowResetPassword(!showResetPassword)}\n                    >\n                      {showResetPassword ? (\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                          <path d=\"M10 12a2 2 0 100-4 2 2 0 000 4z\" />\n                          <path fillRule=\"evenodd\" d=\"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\" clipRule=\"evenodd\" />\n                        </svg>\n                      ) : (\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                          <path fillRule=\"evenodd\" d=\"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z\" clipRule=\"evenodd\" />\n                          <path d=\"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z\" />\n                        </svg>\n                      )}\n                    </button>\n                  </div>\n                  <p className=\"mt-1 text-sm text-gray-500\">Password must be at least 6 characters.</p>\n                </div>\n              </div>\n              <div className=\"flex justify-end space-x-4 mt-6\">\n                <motion.button\n                  type=\"button\"\n                  onClick={() => setShowResetPasswordModal(false)}\n                  className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  Cancel\n                </motion.button>\n                <motion.button\n                  type=\"button\"\n                  onClick={handleResetPassword}\n                  className=\"px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 font-medium transition-colors shadow-md hover:shadow-lg\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  Reset Password\n                </motion.button>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      )}\n\n      {/* Reset All Passwords Modal */}\n      {showResetAllPasswordsModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <motion.div\n            initial={{ scale: 0.9, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            className=\"bg-white rounded-2xl shadow-2xl w-full max-w-md\"\n          >\n            <div className=\"p-6\">\n              <div className=\"flex justify-between items-center mb-6\">\n                <h2 className=\"text-2xl font-bold text-yellow-600\">Reset All Passwords</h2>\n                <button onClick={() => setShowResetAllPasswordsModal(false)} className=\"text-gray-400 hover:text-gray-500\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n              <div className=\"space-y-4\">\n                <div>\n                  <p className=\"text-gray-700 mb-4\">\n                    This will reset the passwords for <strong>ALL</strong> accounts in the system.\n                    Are you sure you want to continue?\n                  </p>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Default Password*</label>\n                  <div className=\"relative\">\n                    <input\n                      type={showDefaultPassword ? \"text\" : \"password\"}\n                      value={defaultPassword}\n                      onChange={(e) => setDefaultPassword(e.target.value)}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-10\"\n                      placeholder=\"Enter default password for all accounts\"\n                      required\n                    />\n                    <button\n                      type=\"button\"\n                      className=\"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700\"\n                      onClick={() => setShowDefaultPassword(!showDefaultPassword)}\n                    >\n                      {showDefaultPassword ? (\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                          <path d=\"M10 12a2 2 0 100-4 2 2 0 000 4z\" />\n                          <path fillRule=\"evenodd\" d=\"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\" clipRule=\"evenodd\" />\n                        </svg>\n                      ) : (\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                          <path fillRule=\"evenodd\" d=\"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z\" clipRule=\"evenodd\" />\n                          <path d=\"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z\" />\n                        </svg>\n                      )}\n                    </button>\n                  </div>\n                  <p className=\"mt-1 text-sm text-gray-500\">Password must be at least 6 characters.</p>\n                </div>\n              </div>\n              <div className=\"flex justify-end space-x-4 mt-6\">\n                <motion.button\n                  type=\"button\"\n                  onClick={() => setShowResetAllPasswordsModal(false)}\n                  className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  Cancel\n                </motion.button>\n                <motion.button\n                  type=\"button\"\n                  onClick={handleResetAllPasswords}\n                  className=\"px-6 py-2 bg-gradient-to-r from-yellow-500 to-yellow-700 text-white rounded-lg hover:from-yellow-600 hover:to-yellow-800 font-medium transition-colors shadow-md hover:shadow-lg\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  Reset All Passwords\n                </motion.button>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Accounts;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,QAAQ,gBAAgB;AAC3E,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAAC0C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC4C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACkD,cAAc,EAAEC,iBAAiB,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACoD,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0D,0BAA0B,EAAEC,6BAA6B,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAAC4D,eAAe,EAAEC,kBAAkB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC8D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACgE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACrE;EACA,MAAMkE,QAAQ,GAAGhE,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiE,IAAI;IAAEC;EAAM,CAAC,GAAGtD,OAAO,CAAC,CAAC;EAEjC,MAAM,CAACuD,QAAQ,EAAEC,WAAW,CAAC,GAAGtE,QAAQ,CAAC;IACvCuE,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE;EAEhB,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,MAAM8E,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI,CAACZ,IAAI,IAAI,CAACC,KAAK,EAAE;QACnBvC,QAAQ,CAAC,iCAAiC,CAAC;QAC3CF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,IAAI;QACF,MAAMqD,MAAM,GAAG;UAAEC,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUd,KAAK;UAAG;QAAE,CAAC;QAChE,MAAMe,QAAQ,GAAG,MAAMhF,KAAK,CAACiF,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,eAAe,EAAEP,MAAM,CAAC;QACzFzD,WAAW,CAAC4D,QAAQ,CAACK,IAAI,IAAI,EAAE,CAAC;QAChC/D,mBAAmB,CAAC0D,QAAQ,CAACK,IAAI,IAAI,EAAE,CAAC;MAC1C,CAAC,CAAC,OAAOC,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA,EAAAC,cAAA;QACZC,OAAO,CAACnE,KAAK,CAAC,cAAc,EAAE,EAAA8D,aAAA,GAAAD,GAAG,CAACN,QAAQ,cAAAO,aAAA,uBAAZA,aAAA,CAAcF,IAAI,KAAIC,GAAG,CAACO,OAAO,CAAC;QAChE,MAAMC,YAAY,GAAG,EAAAN,cAAA,GAAAF,GAAG,CAACN,QAAQ,cAAAQ,cAAA,uBAAZA,cAAA,CAAcO,MAAM,MAAK,GAAG,GAC7C,oCAAoC,GACpC,EAAAN,cAAA,GAAAH,GAAG,CAACN,QAAQ,cAAAS,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcJ,IAAI,cAAAK,mBAAA,uBAAlBA,mBAAA,CAAoBG,OAAO,KAAI,yBAAyB;QAC5DnE,QAAQ,CAACoE,YAAY,CAAC;QACtB,IAAI,EAAAH,cAAA,GAAAL,GAAG,CAACN,QAAQ,cAAAW,cAAA,uBAAZA,cAAA,CAAcI,MAAM,MAAK,GAAG,EAAE;UAChChC,QAAQ,CAAC,QAAQ,CAAC;QACpB;MACF,CAAC,SAAS;QACRvC,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDoD,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACZ,IAAI,EAAEC,KAAK,EAAEF,QAAQ,CAAC,CAAC;EAE3BjE,SAAS,CAAC,MAAM;IACd,MAAMkG,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI,CAAChC,IAAI,IAAI,CAACC,KAAK,EAAE;MAErB,IAAI;QACF,MAAMY,MAAM,GAAG;UAAEC,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUd,KAAK;UAAG;QAAE,CAAC;QAChE,MAAMe,QAAQ,GAAG,MAAMhF,KAAK,CAACiF,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,mBAAmB,EAAEP,MAAM,CAAC;QAC7FvC,eAAe,CAAC0C,QAAQ,CAACK,IAAI,IAAI,EAAE,CAAC;MACtC,CAAC,CAAC,OAAOC,GAAG,EAAE;QAAA,IAAAW,cAAA;QACZL,OAAO,CAACnE,KAAK,CAAC,8BAA8B,EAAE,EAAAwE,cAAA,GAAAX,GAAG,CAACN,QAAQ,cAAAiB,cAAA,uBAAZA,cAAA,CAAcZ,IAAI,KAAIC,GAAG,CAACO,OAAO,CAAC;MAClF;IACF,CAAC;IAEDG,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAAChC,IAAI,EAAEC,KAAK,CAAC,CAAC;EAIjBnE,SAAS,CAAC,MAAM;IACd,MAAMoG,QAAQ,GAAG/E,QAAQ,CAACgF,MAAM,CAACC,OAAO,IAAI;MAC1C,MAAMC,aAAa,GAAGD,OAAO,CAAChC,KAAK,CAACkC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtE,UAAU,CAACqE,WAAW,CAAC,CAAC,CAAC,IAC/DF,OAAO,CAAC9B,IAAI,CAACgC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtE,UAAU,CAACqE,WAAW,CAAC,CAAC,CAAC;MAClF,MAAME,WAAW,GAAGrE,UAAU,GAAGiE,OAAO,CAAC7B,IAAI,KAAKpC,UAAU,GAAG,IAAI;MACnE,OAAOkE,aAAa,IAAIG,WAAW;IACrC,CAAC,CAAC;IACFlF,mBAAmB,CAAC4E,QAAQ,CAAC;EAC/B,CAAC,EAAE,CAACjE,UAAU,EAAEE,UAAU,EAAEhB,QAAQ,CAAC,CAAC;EAEtC,MAAMsF,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEpC,IAAI;MAAEqC;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCzC,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACI,IAAI,GAAGqC;IAAM,CAAC,CAAC;EAC7C,CAAC;EAED,MAAME,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB,IAAI;MACF;MACA,IAAI5C,QAAQ,CAACK,IAAI,KAAK,SAAS,IAAI,CAACL,QAAQ,CAACM,SAAS,EAAE;QACtD9C,QAAQ,CAAC,6CAA6C,CAAC;QACvD;MACF;MAEA,IAAI,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC6E,QAAQ,CAACrC,QAAQ,CAACK,IAAI,CAAC,IAAI,CAACL,QAAQ,CAACO,YAAY,EAAE;QACrG/C,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;MAEA,MAAMmD,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUd,KAAK;QAAG;MAAE,CAAC;MAChE;MACA,MAAM8C,WAAW,GAAG;QAAE,GAAG7C;MAAS,CAAC;MACnC,IAAIA,QAAQ,CAACK,IAAI,KAAK,SAAS,EAAE;QAC/B,OAAOwC,WAAW,CAACvC,SAAS;MAC9B;;MAIA;MACAoB,OAAO,CAACoB,GAAG,CAAC,6BAA6B,EAAE;QACzC,GAAGD,WAAW;QACd1C,QAAQ,EAAE0C,WAAW,CAAC1C,QAAQ,CAAC4C,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC;MACzD,CAAC,CAAC;MAEF,MAAMjH,KAAK,CAACkH,IAAI,CAAC,GAAGhC,OAAO,CAACC,GAAG,CAACC,iBAAiB,eAAe,EAAE2B,WAAW,EAAElC,MAAM,CAAC;MACtFjD,YAAY,CAAC,KAAK,CAAC;MACnBuC,WAAW,CAAC;QACVC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,SAAS;QACfC,SAAS,EAAE,EAAE;QACbC,YAAY,EAAE;MAEhB,CAAC,CAAC;MACF/C,QAAQ,CAAC,EAAE,CAAC;MACZ;MACA,MAAMsD,QAAQ,GAAG,MAAMhF,KAAK,CAACiF,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,eAAe,EAAEP,MAAM,CAAC;MACzFzD,WAAW,CAAC4D,QAAQ,CAACK,IAAI,IAAI,EAAE,CAAC;;MAEhC;MACAvC,eAAe,CAAC,eAAe,CAAC;MAChCE,iBAAiB,CAAC,sCAAsC,CAAC;MACzDJ,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAO0C,GAAG,EAAE;MAAA,IAAA6B,cAAA,EAAAC,mBAAA;MACZ1F,QAAQ,CAAC,EAAAyF,cAAA,GAAA7B,GAAG,CAACN,QAAQ,cAAAmC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc9B,IAAI,cAAA+B,mBAAA,uBAAlBA,mBAAA,CAAoBvB,OAAO,KAAI,0BAA0B,CAAC;IACrE;EACF,CAAC;EAED,MAAMwB,kBAAkB,GAAIjB,OAAO,IAAK;IACtCpE,kBAAkB,CAACoE,OAAO,CAAC;IAC3BtE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMwF,iBAAiB,GAAGA,CAACZ,CAAC,EAAEa,SAAS,KAAK;IAC1Cb,CAAC,CAACc,eAAe,CAAC,CAAC;IACnB9E,oBAAoB,CAAC6E,SAAS,CAAC;IAC/B/E,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMiF,wBAAwB,GAAGA,CAACf,CAAC,EAAEa,SAAS,KAAK;IACjDb,CAAC,CAACc,eAAe,CAAC,CAAC;IACnBpE,kBAAkB,CAACmE,SAAS,CAAC;IAC7BjE,cAAc,CAAC,EAAE,CAAC;IAClBJ,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMwE,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,IAAI,CAACrE,WAAW,IAAIA,WAAW,CAACsE,MAAM,GAAG,CAAC,EAAE;QAC1CjG,QAAQ,CAAC,wCAAwC,CAAC;QAClD;MACF;MAEA,MAAMmD,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUd,KAAK;QAAG;MAAE,CAAC;MAChE,MAAMjE,KAAK,CAACkH,IAAI,CAAC,GAAGhC,OAAO,CAACC,GAAG,CAACC,iBAAiB,iBAAiBjC,eAAe,iBAAiB,EAChG;QAAEE;MAAY,CAAC,EACfwB,MACF,CAAC;MAED3B,yBAAyB,CAAC,KAAK,CAAC;MAChCI,cAAc,CAAC,EAAE,CAAC;;MAElB;MACAR,eAAe,CAAC,gBAAgB,CAAC;MACjCE,iBAAiB,CAAC,uCAAuC,CAAC;MAC1DJ,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAO0C,GAAG,EAAE;MAAA,IAAAsC,cAAA,EAAAC,mBAAA;MACZnG,QAAQ,CAAC,EAAAkG,cAAA,GAAAtC,GAAG,CAACN,QAAQ,cAAA4C,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcvC,IAAI,cAAAwC,mBAAA,uBAAlBA,mBAAA,CAAoBhC,OAAO,KAAI,0BAA0B,CAAC;IACrE;EACF,CAAC;EAED,MAAMiC,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI;MACF,IAAI,CAACrE,eAAe,IAAIA,eAAe,CAACkE,MAAM,GAAG,CAAC,EAAE;QAClDjG,QAAQ,CAAC,wCAAwC,CAAC;QAClD;MACF;MAEA,MAAMmD,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUd,KAAK;QAAG;MAAE,CAAC;MAChE,MAAMe,QAAQ,GAAG,MAAMhF,KAAK,CAACkH,IAAI,CAAC,GAAGhC,OAAO,CAACC,GAAG,CAACC,iBAAiB,+BAA+B,EAC/F;QAAE3B;MAAgB,CAAC,EACnBoB,MACF,CAAC;MAEDrB,6BAA6B,CAAC,KAAK,CAAC;MACpCE,kBAAkB,CAAC,EAAE,CAAC;;MAEtB;MACAZ,eAAe,CAAC,qBAAqB,CAAC;MACtCE,iBAAiB,CAAC,+CAA+C+E,IAAI,CAACC,SAAS,CAAChD,QAAQ,CAACK,IAAI,CAAC4C,OAAO,CAAC,EAAE,CAAC;MACzGrF,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAO0C,GAAG,EAAE;MAAA,IAAA4C,cAAA,EAAAC,mBAAA;MACZzG,QAAQ,CAAC,EAAAwG,cAAA,GAAA5C,GAAG,CAACN,QAAQ,cAAAkD,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc7C,IAAI,cAAA8C,mBAAA,uBAAlBA,mBAAA,CAAoBtC,OAAO,KAAI,+BAA+B,CAAC;IAC1E;EACF,CAAC;EAED,MAAMuC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMvD,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUd,KAAK;QAAG;MAAE,CAAC;MAChE,MAAMjE,KAAK,CAACqI,MAAM,CAAC,GAAGnD,OAAO,CAACC,GAAG,CAACC,iBAAiB,iBAAiB3C,iBAAiB,EAAE,EAAEoC,MAAM,CAAC;MAChGzD,WAAW,CAACD,QAAQ,CAACgF,MAAM,CAAEmC,CAAC,IAAKA,CAAC,CAACC,EAAE,KAAK9F,iBAAiB,CAAC,CAAC;MAC/DD,mBAAmB,CAAC,KAAK,CAAC;MAC1BE,oBAAoB,CAAC,IAAI,CAAC;;MAE1B;MACAI,eAAe,CAAC,iBAAiB,CAAC;MAClCE,iBAAiB,CAAC,wCAAwC,CAAC;MAC3DJ,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAO0C,GAAG,EAAE;MAAA,IAAAkD,cAAA,EAAAC,mBAAA;MACZ/G,QAAQ,CAAC,EAAA8G,cAAA,GAAAlD,GAAG,CAACN,QAAQ,cAAAwD,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcnD,IAAI,cAAAoD,mBAAA,uBAAlBA,mBAAA,CAAoB5C,OAAO,KAAI,0BAA0B,CAAC;MACnErD,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAMkG,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEE,UAAU,EAAE;QAAEC,eAAe,EAAE;MAAI;IAAE;EAC3D,CAAC;EAED,MAAMC,IAAI,GAAG;IACXL,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAG,CAAC;IAC7BJ,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAE;EAC3B,CAAC;EAED,IAAI1H,OAAO,EAAE;IACX,oBAAOT,OAAA,CAACN,MAAM;MAAA0I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnB;EAEA,oBACEvI,OAAA;IAAKwI,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvCzI,OAAA,CAACF,iBAAiB;MAAC4I,MAAM,EAAEvI,WAAY;MAACwI,SAAS,EAAEvI;IAAe;MAAAgI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAErEvI,OAAA;MAAKwI,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDzI,OAAA,CAACP,MAAM;QAACmJ,aAAa,EAAEA,CAAA,KAAMxI,cAAc,CAAC,CAACD,WAAW;MAAE;QAAAiI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE7DvI,OAAA;QAAMwI,SAAS,EAAC,0FAA0F;QAAAC,QAAA,eACxGzI,OAAA;UAAKwI,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAC/B9H,KAAK,iBACJX,OAAA,CAACb,MAAM,CAAC0J,GAAG;YACTC,OAAO,EAAE;cAAEhB,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCY,OAAO,EAAE;cAAEjB,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE;YAAE,CAAE;YAC9BK,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAE7EzI,OAAA;cAAKwI,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCzI,OAAA;gBAAKgJ,KAAK,EAAC,4BAA4B;gBAACR,SAAS,EAAC,2BAA2B;gBAACS,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAT,QAAA,eACnHzI,OAAA;kBAAMmJ,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,mNAAmN;kBAACC,QAAQ,EAAC;gBAAS;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjQ,CAAC,eACNvI,OAAA;gBAAGwI,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAE9H;cAAK;gBAAAyH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eAEDvI,OAAA,CAACb,MAAM,CAAC0J,GAAG;YACTC,OAAO,EAAE;cAAEhB,OAAO,EAAE;YAAE,CAAE;YACxBiB,OAAO,EAAE;cAAEjB,OAAO,EAAE;YAAE,CAAE;YACxBE,UAAU,EAAE;cAAEsB,QAAQ,EAAE;YAAI,CAAE;YAAAb,QAAA,gBAE9BzI,OAAA;cAAKwI,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/FzI,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAIwI,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChFvI,OAAA;kBAAGwI,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAoC;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACNvI,OAAA;gBAAKwI,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,gBAC/DzI,OAAA,CAACb,MAAM,CAACoK,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAM;oBACb/G,kBAAkB,CAAC,EAAE,CAAC;oBACtBF,6BAA6B,CAAC,IAAI,CAAC;kBACrC,CAAE;kBACF8F,SAAS,EAAC,sMAAsM;kBAAAC,QAAA,gBAEhNzI,OAAA;oBAAKgJ,KAAK,EAAC,4BAA4B;oBAACR,SAAS,EAAC,cAAc;oBAACU,IAAI,EAAC,MAAM;oBAACD,OAAO,EAAC,WAAW;oBAACW,MAAM,EAAC,cAAc;oBAAAnB,QAAA,eACpHzI,OAAA;sBAAM6J,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACX,CAAC,EAAC;oBAA4H;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjM,CAAC,uBAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAChBvI,OAAA,CAACb,MAAM,CAACoK,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAM7I,YAAY,CAAC,IAAI,CAAE;kBAClC0H,SAAS,EAAC,oMAAoM;kBAAAC,QAAA,gBAE9MzI,OAAA,CAACX,MAAM;oBAACmJ,SAAS,EAAC;kBAAc;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAErC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvI,OAAA;cAAKwI,SAAS,EAAC,sCAAsC;cAAAC,QAAA,gBACnDzI,OAAA;gBAAKwI,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BzI,OAAA,CAACV,QAAQ;kBAACkJ,SAAS,EAAC;gBAA6C;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpEvI,OAAA;kBACEgK,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,4BAA4B;kBACxCpE,KAAK,EAAE1E,UAAW;kBAClB+I,QAAQ,EAAGtE,CAAC,IAAKxE,aAAa,CAACwE,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;kBAC/C2C,SAAS,EAAC;gBAAmH;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9H,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvI,OAAA;gBACE6F,KAAK,EAAExE,UAAW;gBAClB6I,QAAQ,EAAGtE,CAAC,IAAKtE,aAAa,CAACsE,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;gBAC/C2C,SAAS,EAAC,sGAAsG;gBAAAC,QAAA,gBAEhHzI,OAAA;kBAAQ6F,KAAK,EAAC,EAAE;kBAAA4C,QAAA,EAAC;gBAAS;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnCvI,OAAA;kBAAQ6F,KAAK,EAAC,SAAS;kBAAA4C,QAAA,EAAC;gBAAO;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxCvI,OAAA;kBAAQ6F,KAAK,EAAC,YAAY;kBAAA4C,QAAA,EAAC;gBAAU;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9CvI,OAAA;kBAAQ6F,KAAK,EAAC,OAAO;kBAAA4C,QAAA,EAAC;gBAAK;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCvI,OAAA;kBAAQ6F,KAAK,EAAC,WAAW;kBAAA4C,QAAA,EAAC;gBAAS;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENvI,OAAA,CAACb,MAAM,CAAC0J,GAAG;cACTsB,QAAQ,EAAEvC,SAAU;cACpBkB,OAAO,EAAC,QAAQ;cAChBC,OAAO,EAAC,MAAM;cACQ;cACtBP,SAAS,EAAC,kHAAkH;cAAAC,QAAA,eAE5HzI,OAAA;gBAAKwI,SAAS,EAAC,KAAK;gBAAAC,QAAA,eAClBzI,OAAA;kBAAKwI,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC9BzI,OAAA;oBAAOwI,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,gBACpDzI,OAAA;sBAAOwI,SAAS,EAAC,YAAY;sBAAAC,QAAA,eAC3BzI,OAAA;wBAAAyI,QAAA,gBACEzI,OAAA;0BAAIwI,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAK;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACzGvI,OAAA;0BAAIwI,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAI;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACxGvI,OAAA;0BAAIwI,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAI;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACxGvI,OAAA;0BAAIwI,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAa;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACjHvI,OAAA;0BAAIwI,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAU;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAE9GvI,OAAA;0BAAIwI,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAO;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACRvI,OAAA;sBAAOwI,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EACjDlI,gBAAgB,CAACsG,MAAM,KAAK,CAAC,gBAC5B7G,OAAA;wBAAAyI,QAAA,eACEzI,OAAA;0BAAIoK,OAAO,EAAC,GAAG;0BAAC5B,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,eAC/CzI,OAAA;4BAAKwI,SAAS,EAAC,2CAA2C;4BAAAC,QAAA,gBACxDzI,OAAA,CAACZ,OAAO;8BAACoJ,SAAS,EAAC;4BAA8B;8BAAAJ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eACpDvI,OAAA;8BAAIwI,SAAS,EAAC,mCAAmC;8BAAAC,QAAA,EAAC;4BAAiB;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACxEvI,OAAA;8BAAGwI,SAAS,EAAC,oBAAoB;8BAAAC,QAAA,EAAC;4BAAiC;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,GAELhI,gBAAgB,CAAC8J,GAAG,CAAC,CAAC/E,OAAO,EAAEgF,KAAK,kBAClCtK,OAAA,CAACb,MAAM,CAACoL,EAAE;wBACkC;wBAC1CJ,QAAQ,EAAEjC,IAAK;wBACfM,SAAS,EAAC,iCAAiC;wBAC3CmB,OAAO,EAAEA,CAAA,KAAMpD,kBAAkB,CAACjB,OAAO,CAAE;wBAAAmD,QAAA,gBAE3CzI,OAAA;0BAAIwI,SAAS,EAAC,mDAAmD;0BAAAC,QAAA,EAAEnD,OAAO,CAAChC;wBAAK;0BAAA8E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACtFvI,OAAA;0BAAIwI,SAAS,EAAC,mDAAmD;0BAAAC,QAAA,EAAEnD,OAAO,CAAC9B;wBAAI;0BAAA4E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACrFvI,OAAA;0BAAIwI,SAAS,EAAC,mDAAmD;0BAAAC,QAAA,EAAEnD,OAAO,CAAC7B;wBAAI;0BAAA2E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACrFvI,OAAA;0BAAIwI,SAAS,EAAC,mDAAmD;0BAAAC,QAAA,EAAEnD,OAAO,CAAC3B,YAAY,IAAI;wBAAK;0BAAAyE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACtGvI,OAAA;0BAAIwI,SAAS,EAAC,mDAAmD;0BAAAC,QAAA,EAAEnD,OAAO,CAAC5B,SAAS,IAAI;wBAAK;0BAAA0E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAEnGvI,OAAA;0BAAIwI,SAAS,EAAC,mDAAmD;0BAAAC,QAAA,eAC/DzI,OAAA;4BAAKwI,SAAS,EAAC,gBAAgB;4BAAAC,QAAA,gBAC7BzI,OAAA;8BACE2J,OAAO,EAAG/D,CAAC,IAAKe,wBAAwB,CAACf,CAAC,EAAEN,OAAO,CAACmC,EAAE,CAAE;8BACxDe,SAAS,EAAC,qCAAqC;8BAC/CgC,KAAK,EAAC,gBAAgB;8BAAA/B,QAAA,eAEtBzI,OAAA;gCAAKgJ,KAAK,EAAC,4BAA4B;gCAACR,SAAS,EAAC,SAAS;gCAACU,IAAI,EAAC,MAAM;gCAACD,OAAO,EAAC,WAAW;gCAACW,MAAM,EAAC,cAAc;gCAAAnB,QAAA,eAC/GzI,OAAA;kCAAM6J,aAAa,EAAC,OAAO;kCAACC,cAAc,EAAC,OAAO;kCAACC,WAAW,EAAE,CAAE;kCAACX,CAAC,EAAC;gCAA4H;kCAAAhB,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACjM;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACA,CAAC,eACTvI,OAAA;8BACE2J,OAAO,EAAG/D,CAAC,IAAKY,iBAAiB,CAACZ,CAAC,EAAEN,OAAO,CAACmC,EAAE,CAAE;8BACjDe,SAAS,EAAC,iCAAiC;8BAC3CgC,KAAK,EAAC,QAAQ;8BAAA/B,QAAA,eAEdzI,OAAA,CAACR,OAAO;gCAACgJ,SAAS,EAAC;8BAAS;gCAAAJ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACzB,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA,GA9BAjD,OAAO,CAACmC,EAAE,IAAInC,OAAO,CAAChC,KAAK,GAAGgH,KAAK;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA+B/B,CACZ;oBACF;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GApEDlI,QAAQ,CAACwG,MAAM;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqEV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAEL1H,SAAS,iBACRb,OAAA;MAAKwI,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7FzI,OAAA,CAACb,MAAM,CAAC0J,GAAG;QACTC,OAAO,EAAE;UAAEW,KAAK,EAAE,GAAG;UAAE3B,OAAO,EAAE;QAAE,CAAE;QACpCiB,OAAO,EAAE;UAAEU,KAAK,EAAE,CAAC;UAAE3B,OAAO,EAAE;QAAE,CAAE;QAClCU,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAEzFzI,OAAA;UAAKwI,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBzI,OAAA;YAAKwI,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDzI,OAAA;cAAIwI,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAW;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClEvI,OAAA;cAAQ2J,OAAO,EAAEA,CAAA,KAAM7I,YAAY,CAAC,KAAK,CAAE;cAAC0H,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eACvFzI,OAAA;gBAAKgJ,KAAK,EAAC,4BAA4B;gBAACR,SAAS,EAAC,SAAS;gBAACU,IAAI,EAAC,MAAM;gBAACD,OAAO,EAAC,WAAW;gBAACW,MAAM,EAAC,cAAc;gBAAAnB,QAAA,eAC/GzI,OAAA;kBAAM6J,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACX,CAAC,EAAC;gBAAsB;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNvI,OAAA;YAAMyK,QAAQ,EAAE1E,YAAa;YAACyC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACjDzI,OAAA;cAAKwI,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDzI,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOwI,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAM;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9EvI,OAAA;kBACEgK,IAAI,EAAC,OAAO;kBACZxG,IAAI,EAAC,OAAO;kBACZqC,KAAK,EAAEzC,QAAQ,CAACE,KAAM;kBACtB4G,QAAQ,EAAEvE,iBAAkB;kBAC5B6C,SAAS,EAAC,2GAA2G;kBACrHkC,QAAQ;gBAAA;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvI,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOwI,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAS;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjFvI,OAAA;kBAAKwI,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBzI,OAAA;oBACEgK,IAAI,EAAEpG,YAAY,GAAG,MAAM,GAAG,UAAW;oBACzCJ,IAAI,EAAC,UAAU;oBACfqC,KAAK,EAAEzC,QAAQ,CAACG,QAAS;oBACzB2G,QAAQ,EAAEvE,iBAAkB;oBAC5B6C,SAAS,EAAC,mHAAmH;oBAC7HkC,QAAQ;kBAAA;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACFvI,OAAA;oBACEgK,IAAI,EAAC,QAAQ;oBACbxB,SAAS,EAAC,qFAAqF;oBAC/FmB,OAAO,EAAEA,CAAA,KAAM9F,eAAe,CAAC,CAACD,YAAY,CAAE;oBAAA6E,QAAA,EAE7C7E,YAAY,gBACX5D,OAAA;sBAAKgJ,KAAK,EAAC,4BAA4B;sBAACR,SAAS,EAAC,SAAS;sBAACS,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,cAAc;sBAAAT,QAAA,gBACjGzI,OAAA;wBAAMoJ,CAAC,EAAC;sBAAiC;wBAAAhB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC5CvI,OAAA;wBAAMmJ,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,yIAAyI;wBAACC,QAAQ,EAAC;sBAAS;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvL,CAAC,gBAENvI,OAAA;sBAAKgJ,KAAK,EAAC,4BAA4B;sBAACR,SAAS,EAAC,SAAS;sBAACS,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,cAAc;sBAAAT,QAAA,gBACjGzI,OAAA;wBAAMmJ,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,8PAA8P;wBAACC,QAAQ,EAAC;sBAAS;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC/SvI,OAAA;wBAAMoJ,CAAC,EAAC;sBAA6I;wBAAAhB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrJ;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvI,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOwI,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAK;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7EvI,OAAA;kBACEgK,IAAI,EAAC,MAAM;kBACXxG,IAAI,EAAC,MAAM;kBACXqC,KAAK,EAAEzC,QAAQ,CAACI,IAAK;kBACrB0G,QAAQ,EAAEvE,iBAAkB;kBAC5B6C,SAAS,EAAC,6GAA6G;kBACvHkC,QAAQ;gBAAA;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvI,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOwI,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAK;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7EvI,OAAA;kBACEwD,IAAI,EAAC,MAAM;kBACXqC,KAAK,EAAEzC,QAAQ,CAACK,IAAK;kBACrByG,QAAQ,EAAEvE,iBAAkB;kBAC5B6C,SAAS,EAAC,6GAA6G;kBACvHkC,QAAQ;kBAAAjC,QAAA,gBAERzI,OAAA;oBAAQ6F,KAAK,EAAC,SAAS;oBAAA4C,QAAA,EAAC;kBAAO;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxCvI,OAAA;oBAAQ6F,KAAK,EAAC,YAAY;oBAAA4C,QAAA,EAAC;kBAAU;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9CvI,OAAA;oBAAQ6F,KAAK,EAAC,OAAO;oBAAA4C,QAAA,EAAC;kBAAK;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpCvI,OAAA;oBAAQ6F,KAAK,EAAC,WAAW;oBAAA4C,QAAA,EAAC;kBAAS;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EACLnF,QAAQ,CAACK,IAAI,KAAK,SAAS,iBAC1BzD,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOwI,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAW;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnFvI,OAAA;kBACEgK,IAAI,EAAC,MAAM;kBACXxG,IAAI,EAAC,WAAW;kBAChBqC,KAAK,EAAEzC,QAAQ,CAACM,SAAU;kBAC1BwG,QAAQ,EAAEvE,iBAAkB;kBAC5B6C,SAAS,EAAC,2GAA2G;kBACrHkC,QAAQ;gBAAA;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,EACA,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC9C,QAAQ,CAACrC,QAAQ,CAACK,IAAI,CAAC,iBACtEzD,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOwI,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAW;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnFvI,OAAA;kBACEwD,IAAI,EAAC,cAAc;kBACnBqC,KAAK,EAAEzC,QAAQ,CAACO,YAAa;kBAC7BuG,QAAQ,EAAEvE,iBAAkB;kBAC5B6C,SAAS,EAAC,2GAA2G;kBACrHkC,QAAQ;kBAAAjC,QAAA,gBAERzI,OAAA;oBAAQ6F,KAAK,EAAC,EAAE;oBAAA4C,QAAA,EAAC;kBAAiB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC1ChH,YAAY,CAAC8I,GAAG,CAAEM,UAAU,iBAC3B3K,OAAA;oBAAsC6F,KAAK,EAAE8E,UAAU,CAAChH,YAAa;oBAAA8E,QAAA,GAClEkC,UAAU,CAACnH,IAAI,CAACoH,EAAE,EAAC,IAAE,EAACD,UAAU,CAAChH,YAAY,EAAC,GACjD;kBAAA,GAFagH,UAAU,CAAChH,YAAY;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAE5B,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGE,CAAC,eACNvI,OAAA;cAAKwI,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9CzI,OAAA,CAACb,MAAM,CAACoK,MAAM;gBACZS,IAAI,EAAC,QAAQ;gBACbL,OAAO,EAAEA,CAAA,KAAM7I,YAAY,CAAC,KAAK,CAAE;gBACnC0H,SAAS,EAAC,0GAA0G;gBACpHgB,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAAhB,QAAA,EAC3B;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,eAChBvI,OAAA,CAACb,MAAM,CAACoK,MAAM;gBACZS,IAAI,EAAC,QAAQ;gBACbxB,SAAS,EAAC,0KAA0K;gBACpLgB,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAAhB,QAAA,EAC3B;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,EAEAxH,gBAAgB,IAAIE,eAAe,iBAClCjB,OAAA;MAAKwI,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7FzI,OAAA,CAACb,MAAM,CAAC0J,GAAG;QACTC,OAAO,EAAE;UAAEW,KAAK,EAAE,GAAG;UAAE3B,OAAO,EAAE;QAAE,CAAE;QACpCiB,OAAO,EAAE;UAAEU,KAAK,EAAE,CAAC;UAAE3B,OAAO,EAAE;QAAE,CAAE;QAClCU,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAE5DzI,OAAA;UAAKwI,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBzI,OAAA;YAAKwI,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDzI,OAAA;cAAKwI,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCzI,OAAA;gBAAKwI,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,GACzFxH,eAAe,CAACwC,IAAI,KAAK,SAAS,iBACjCzD,OAAA;kBAAKgJ,KAAK,EAAC,4BAA4B;kBAACR,SAAS,EAAC,SAAS;kBAACU,IAAI,EAAC,MAAM;kBAACD,OAAO,EAAC,WAAW;kBAACW,MAAM,EAAC,cAAc;kBAAAnB,QAAA,gBAC/GzI,OAAA;oBAAMoJ,CAAC,EAAC;kBAAyB;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpCvI,OAAA;oBAAMoJ,CAAC,EAAC;kBAA+I;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1JvI,OAAA;oBAAM6J,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACX,CAAC,EAAC;kBAAsL;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3P,CACN,EACAtH,eAAe,CAACwC,IAAI,KAAK,YAAY,iBACpCzD,OAAA;kBAAKgJ,KAAK,EAAC,4BAA4B;kBAACR,SAAS,EAAC,SAAS;kBAACU,IAAI,EAAC,MAAM;kBAACD,OAAO,EAAC,WAAW;kBAACW,MAAM,EAAC,cAAc;kBAAAnB,QAAA,eAC/GzI,OAAA;oBAAM6J,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACX,CAAC,EAAC;kBAAgM;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrQ,CACN,EACAtH,eAAe,CAACwC,IAAI,KAAK,OAAO,iBAC/BzD,OAAA;kBAAKgJ,KAAK,EAAC,4BAA4B;kBAACR,SAAS,EAAC,SAAS;kBAACU,IAAI,EAAC,MAAM;kBAACD,OAAO,EAAC,WAAW;kBAACW,MAAM,EAAC,cAAc;kBAAAnB,QAAA,gBAC/GzI,OAAA;oBAAM6J,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACX,CAAC,EAAC;kBAAqe;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7iBvI,OAAA;oBAAM6J,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACX,CAAC,EAAC;kBAAkC;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG,CACN,EACAtH,eAAe,CAACwC,IAAI,KAAK,WAAW,iBACnCzD,OAAA;kBAAKgJ,KAAK,EAAC,4BAA4B;kBAACR,SAAS,EAAC,SAAS;kBAACU,IAAI,EAAC,MAAM;kBAACD,OAAO,EAAC,WAAW;kBAACW,MAAM,EAAC,cAAc;kBAAAnB,QAAA,eAC/GzI,OAAA;oBAAM6J,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACX,CAAC,EAAC;kBAAwQ;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7U,CACN,EACAtH,eAAe,CAACwC,IAAI,KAAK,SAAS,iBACjCzD,OAAA;kBAAKgJ,KAAK,EAAC,4BAA4B;kBAACR,SAAS,EAAC,SAAS;kBAACU,IAAI,EAAC,MAAM;kBAACD,OAAO,EAAC,WAAW;kBAACW,MAAM,EAAC,cAAc;kBAAAnB,QAAA,eAC/GzI,OAAA;oBAAM6J,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACX,CAAC,EAAC;kBAAqE;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1I,CACN,EACAtH,eAAe,CAACwC,IAAI,KAAK,YAAY,iBACpCzD,OAAA;kBAAKgJ,KAAK,EAAC,4BAA4B;kBAACR,SAAS,EAAC,SAAS;kBAACU,IAAI,EAAC,MAAM;kBAACD,OAAO,EAAC,WAAW;kBAACW,MAAM,EAAC,cAAc;kBAAAnB,QAAA,eAC/GzI,OAAA;oBAAM6J,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACX,CAAC,EAAC;kBAAgM;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrQ,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNvI,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAIwI,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAExH,eAAe,CAACuC;gBAAI;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC5EvI,OAAA;kBAAGwI,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAExH,eAAe,CAACwC;gBAAI;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvI,OAAA;cAAQ2J,OAAO,EAAEA,CAAA,KAAM3I,mBAAmB,CAAC,KAAK,CAAE;cAACwH,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eAC9FzI,OAAA;gBAAKgJ,KAAK,EAAC,4BAA4B;gBAACR,SAAS,EAAC,SAAS;gBAACU,IAAI,EAAC,MAAM;gBAACD,OAAO,EAAC,WAAW;gBAACW,MAAM,EAAC,cAAc;gBAAAnB,QAAA,eAC/GzI,OAAA;kBAAM6J,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACX,CAAC,EAAC;gBAAsB;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENvI,OAAA;YAAKwI,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7CzI,OAAA;cAAIwI,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAmB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjFvI,OAAA;cAAKwI,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDzI,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOwI,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAK;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxEvI,OAAA;kBAAGwI,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAExH,eAAe,CAACqC;gBAAK;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC,eACNvI,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOwI,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrEvI,OAAA;kBAAGwI,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAExH,eAAe,CAACwG;gBAAE;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eAENvI,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOwI,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvEvI,OAAA;kBAAGwI,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAExH,eAAe,CAACwC;gBAAI;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eACNvI,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOwI,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAU;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7EvI,OAAA;kBAAGwI,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAC1CxH,eAAe,CAAC4J,SAAS,GAAG,IAAIC,IAAI,CAAC7J,eAAe,CAAC4J,SAAS,CAAC,CAACE,cAAc,CAAC,CAAC,GAAG;gBAAK;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNvI,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOwI,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/EvI,OAAA;kBAAGwI,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAC1CxH,eAAe,CAAC+J,SAAS,GAAG,IAAIF,IAAI,CAAC7J,eAAe,CAAC+J,SAAS,CAAC,CAACD,cAAc,CAAC,CAAC,GAAG;gBAAK;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvI,OAAA;YAAKwI,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7CzI,OAAA;cAAIwI,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAyB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvFvI,OAAA;cAAKwI,SAAS,EAAC,uCAAuC;cAAAC,QAAA,GACnDxH,eAAe,CAAC0C,YAAY,iBAC3B3D,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOwI,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChFvI,OAAA;kBAAGwI,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAExH,eAAe,CAAC0C;gBAAY;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CACN,EACAtH,eAAe,CAACyC,SAAS,iBACxB1D,OAAA;gBAAAyI,QAAA,gBACEzI,OAAA;kBAAOwI,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAU;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7EvI,OAAA;kBAAGwI,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAExH,eAAe,CAACyC;gBAAS;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvI,OAAA;YAAKwI,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCzI,OAAA,CAACb,MAAM,CAACoK,MAAM;cACZI,OAAO,EAAEA,CAAA,KAAM;gBACbrH,kBAAkB,CAACrB,eAAe,CAACwG,EAAE,CAAC;gBACtCjF,cAAc,CAAC,EAAE,CAAC;gBAClBM,oBAAoB,CAAC,KAAK,CAAC;gBAC3B9B,mBAAmB,CAAC,KAAK,CAAC;gBAC1BoB,yBAAyB,CAAC,IAAI,CAAC;cACjC,CAAE;cACFoG,SAAS,EAAC,mHAAmH;cAC7HgB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAAAhB,QAAA,gBAE1BzI,OAAA;gBAAKgJ,KAAK,EAAC,4BAA4B;gBAACR,SAAS,EAAC,cAAc;gBAACU,IAAI,EAAC,MAAM;gBAACD,OAAO,EAAC,WAAW;gBAACW,MAAM,EAAC,cAAc;gBAAAnB,QAAA,eACpHzI,OAAA;kBAAM6J,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACX,CAAC,EAAC;gBAA4H;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjM,CAAC,kBAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChBvI,OAAA,CAACb,MAAM,CAACoK,MAAM;cACZI,OAAO,EAAEA,CAAA,KAAM3I,mBAAmB,CAAC,KAAK,CAAE;cAC1CwH,SAAS,EAAC,0GAA0G;cACpHgB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAAAhB,QAAA,EAC3B;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,eAGDvI,OAAA,CAACL,YAAY;MACX+I,MAAM,EAAEjH,gBAAiB;MACzBwJ,OAAO,EAAEA,CAAA,KAAMvJ,mBAAmB,CAAC,KAAK,CAAE;MAC1CwJ,SAAS,EAAE5D,mBAAoB;MAC/BkD,KAAK,EAAC,gBAAgB;MACtBzF,OAAO,EAAC;IAA6E;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC,eAGFvI,OAAA,CAACJ,YAAY;MACX8I,MAAM,EAAE7G,gBAAiB;MACzBoJ,OAAO,EAAEA,CAAA,KAAMnJ,mBAAmB,CAAC,KAAK,CAAE;MAC1C0I,KAAK,EAAEzI,YAAa;MACpBgD,OAAO,EAAE9C;IAAe;MAAAmG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,EAGDpG,sBAAsB,iBACrBnC,OAAA;MAAKwI,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7FzI,OAAA,CAACb,MAAM,CAAC0J,GAAG;QACTC,OAAO,EAAE;UAAEW,KAAK,EAAE,GAAG;UAAE3B,OAAO,EAAE;QAAE,CAAE;QACpCiB,OAAO,EAAE;UAAEU,KAAK,EAAE,CAAC;UAAE3B,OAAO,EAAE;QAAE,CAAE;QAClCU,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAE3DzI,OAAA;UAAKwI,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBzI,OAAA;YAAKwI,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDzI,OAAA;cAAIwI,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAc;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpEvI,OAAA;cAAQ2J,OAAO,EAAEA,CAAA,KAAMvH,yBAAyB,CAAC,KAAK,CAAE;cAACoG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eACpGzI,OAAA;gBAAKgJ,KAAK,EAAC,4BAA4B;gBAACR,SAAS,EAAC,SAAS;gBAACU,IAAI,EAAC,MAAM;gBAACD,OAAO,EAAC,WAAW;gBAACW,MAAM,EAAC,cAAc;gBAAAnB,QAAA,eAC/GzI,OAAA;kBAAM6J,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACX,CAAC,EAAC;gBAAsB;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNvI,OAAA;YAAKwI,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBzI,OAAA;cAAAyI,QAAA,gBACEzI,OAAA;gBAAOwI,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAa;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrFvI,OAAA;gBAAKwI,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBzI,OAAA;kBACEgK,IAAI,EAAEnH,iBAAiB,GAAG,MAAM,GAAG,UAAW;kBAC9CgD,KAAK,EAAEtD,WAAY;kBACnB2H,QAAQ,EAAGtE,CAAC,IAAKpD,cAAc,CAACoD,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;kBAChD2C,SAAS,EAAC,iHAAiH;kBAC3HyB,WAAW,EAAC,oBAAoB;kBAChCS,QAAQ;gBAAA;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACFvI,OAAA;kBACEgK,IAAI,EAAC,QAAQ;kBACbxB,SAAS,EAAC,qFAAqF;kBAC/FmB,OAAO,EAAEA,CAAA,KAAM7G,oBAAoB,CAAC,CAACD,iBAAiB,CAAE;kBAAA4F,QAAA,EAEvD5F,iBAAiB,gBAChB7C,OAAA;oBAAKgJ,KAAK,EAAC,4BAA4B;oBAACR,SAAS,EAAC,SAAS;oBAACS,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,cAAc;oBAAAT,QAAA,gBACjGzI,OAAA;sBAAMoJ,CAAC,EAAC;oBAAiC;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC5CvI,OAAA;sBAAMmJ,QAAQ,EAAC,SAAS;sBAACC,CAAC,EAAC,yIAAyI;sBAACC,QAAQ,EAAC;oBAAS;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvL,CAAC,gBAENvI,OAAA;oBAAKgJ,KAAK,EAAC,4BAA4B;oBAACR,SAAS,EAAC,SAAS;oBAACS,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,cAAc;oBAAAT,QAAA,gBACjGzI,OAAA;sBAAMmJ,QAAQ,EAAC,SAAS;sBAACC,CAAC,EAAC,8PAA8P;sBAACC,QAAQ,EAAC;oBAAS;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/SvI,OAAA;sBAAMoJ,CAAC,EAAC;oBAA6I;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrJ;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNvI,OAAA;gBAAGwI,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAuC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvI,OAAA;YAAKwI,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CzI,OAAA,CAACb,MAAM,CAACoK,MAAM;cACZS,IAAI,EAAC,QAAQ;cACbL,OAAO,EAAEA,CAAA,KAAMvH,yBAAyB,CAAC,KAAK,CAAE;cAChDoG,SAAS,EAAC,0GAA0G;cACpHgB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAAAhB,QAAA,EAC3B;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChBvI,OAAA,CAACb,MAAM,CAACoK,MAAM;cACZS,IAAI,EAAC,QAAQ;cACbL,OAAO,EAAE/C,mBAAoB;cAC7B4B,SAAS,EAAC,0KAA0K;cACpLgB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAAAhB,QAAA,EAC3B;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,EAGA9F,0BAA0B,iBACzBzC,OAAA;MAAKwI,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7FzI,OAAA,CAACb,MAAM,CAAC0J,GAAG;QACTC,OAAO,EAAE;UAAEW,KAAK,EAAE,GAAG;UAAE3B,OAAO,EAAE;QAAE,CAAE;QACpCiB,OAAO,EAAE;UAAEU,KAAK,EAAE,CAAC;UAAE3B,OAAO,EAAE;QAAE,CAAE;QAClCU,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAE3DzI,OAAA;UAAKwI,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBzI,OAAA;YAAKwI,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDzI,OAAA;cAAIwI,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAAmB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3EvI,OAAA;cAAQ2J,OAAO,EAAEA,CAAA,KAAMjH,6BAA6B,CAAC,KAAK,CAAE;cAAC8F,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eACxGzI,OAAA;gBAAKgJ,KAAK,EAAC,4BAA4B;gBAACR,SAAS,EAAC,SAAS;gBAACU,IAAI,EAAC,MAAM;gBAACD,OAAO,EAAC,WAAW;gBAACW,MAAM,EAAC,cAAc;gBAAAnB,QAAA,eAC/GzI,OAAA;kBAAM6J,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACX,CAAC,EAAC;gBAAsB;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNvI,OAAA;YAAKwI,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBzI,OAAA;cAAAyI,QAAA,gBACEzI,OAAA;gBAAGwI,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAAC,oCACE,eAAAzI,OAAA;kBAAAyI,QAAA,EAAQ;gBAAG;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,+DAExD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJvI,OAAA;gBAAOwI,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAiB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzFvI,OAAA;gBAAKwI,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBzI,OAAA;kBACEgK,IAAI,EAAEjH,mBAAmB,GAAG,MAAM,GAAG,UAAW;kBAChD8C,KAAK,EAAElD,eAAgB;kBACvBuH,QAAQ,EAAGtE,CAAC,IAAKhD,kBAAkB,CAACgD,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;kBACpD2C,SAAS,EAAC,iHAAiH;kBAC3HyB,WAAW,EAAC,yCAAyC;kBACrDS,QAAQ;gBAAA;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACFvI,OAAA;kBACEgK,IAAI,EAAC,QAAQ;kBACbxB,SAAS,EAAC,qFAAqF;kBAC/FmB,OAAO,EAAEA,CAAA,KAAM3G,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;kBAAA0F,QAAA,EAE3D1F,mBAAmB,gBAClB/C,OAAA;oBAAKgJ,KAAK,EAAC,4BAA4B;oBAACR,SAAS,EAAC,SAAS;oBAACS,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,cAAc;oBAAAT,QAAA,gBACjGzI,OAAA;sBAAMoJ,CAAC,EAAC;oBAAiC;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC5CvI,OAAA;sBAAMmJ,QAAQ,EAAC,SAAS;sBAACC,CAAC,EAAC,yIAAyI;sBAACC,QAAQ,EAAC;oBAAS;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvL,CAAC,gBAENvI,OAAA;oBAAKgJ,KAAK,EAAC,4BAA4B;oBAACR,SAAS,EAAC,SAAS;oBAACS,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,cAAc;oBAAAT,QAAA,gBACjGzI,OAAA;sBAAMmJ,QAAQ,EAAC,SAAS;sBAACC,CAAC,EAAC,8PAA8P;sBAACC,QAAQ,EAAC;oBAAS;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/SvI,OAAA;sBAAMoJ,CAAC,EAAC;oBAA6I;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrJ;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNvI,OAAA;gBAAGwI,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAuC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvI,OAAA;YAAKwI,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CzI,OAAA,CAACb,MAAM,CAACoK,MAAM;cACZS,IAAI,EAAC,QAAQ;cACbL,OAAO,EAAEA,CAAA,KAAMjH,6BAA6B,CAAC,KAAK,CAAE;cACpD8F,SAAS,EAAC,0GAA0G;cACpHgB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAAAhB,QAAA,EAC3B;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChBvI,OAAA,CAACb,MAAM,CAACoK,MAAM;cACZS,IAAI,EAAC,QAAQ;cACbL,OAAO,EAAE3C,uBAAwB;cACjCwB,SAAS,EAAC,kLAAkL;cAC5LgB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAAAhB,QAAA,EAC3B;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrI,EAAA,CA72BID,QAAQ;EAAA,QA0BKhB,WAAW,EACJY,OAAO;AAAA;AAAAsL,EAAA,GA3B3BlL,QAAQ;AA+2Bd,eAAeA,QAAQ;AAAC,IAAAkL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}