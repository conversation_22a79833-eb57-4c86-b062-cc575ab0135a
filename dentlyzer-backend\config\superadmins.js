// Configuration for authorized emails in ODenta
const config = {
  // Superadmin emails (highest priority)
  superadminEmails: [
    '<EMAIL>',
    '<EMAIL>',
    // Add more superadmin emails below
  ],

  // Other authorized emails for Google Sign-In
  // These users will be created automatically with appropriate roles
  authorizedEmails: {
    // Admin emails
    admins: [
      // Add admin emails here if needed
    ],

    // Supervisor emails
    supervisors: [
      // Add supervisor emails here if needed
    ],

    // Student emails
    students: [
      // Add student emails here if needed
    ],

    // Assistant emails
    assistants: [
      // Add assistant emails here if needed
    ]
  }
};

// Export superadmin emails for backward compatibility
module.exports = config.superadminEmails;

// Export full config
module.exports.config = config;